#INCLUDE "MSMGADD.CH"
#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"  
#INCLUDE "FILEIO.CH"
#INCLUDE "TOTVS.CH"

#DEFINE SEED 4397
#DEFINE ID_FULLS	5
#DEFINE ID_LIGHTS 	6

/***

Prazo de geração totvslic, no periodo de contigencia( será 4 meses a validade)
Destacar a geracao do Arquivo.

*/
User Function CLIA020A() //Autorização de empresa
Local cSvAlias := Alias()

Private cCadastro := FunDesc()
Private aRotina := {{"Gerar Arquivo"	, "U_A020Create" , 0, 3},;
                   	{"Verif. Arquivo"	, "U_A020ChkDisk", 0, 3},;
					{"Pesquisar"     	, "AxPesqui"   , 0, 1},;
                    {"Autorizar"		, "U_A020EmpAut" , 0, 3},;
                    {"I<PERSON><PERSON><PERSON>"			, "U_A019Print"  , 0, 3},;
                    {"Converter"		, "U_A020DlgConv", 0, 3}}

U_CliServ()

MBrowse(6,1,22,75,"ZA1")

If !Empty(cSvAlias)
	DbSelectArea(cSvAlias)
EndIf
Return
       
User Function CLIA020B()  //Hardlock versão 7
U_CLSetVer("7")
Return MyClia020()

User Function CLIA020C()  //Autorização versão 8/10/11
Local cFilbck := cFilAnt
U_CLSetVer("8")
If cFilAnt <> "00001000100"
	ApMsgStop("Opcao disponivel somente para Microsiga. A filial será trocada!")
	SetBrwSeeAll( .F. )
	SetBrwCHGAll( .F. )
	cFilAnt := "00001000100"
EndIf

MyClia020()
           
cFilAnt := cFilBck

Return 

User Function CLIA020E() //Visualização dos hardlocks
Private cCadastro := FunDesc()
Private aRotina := {{"Pesquisar"	  , "AxPesqui"    , 0, 1},;
                    {"Visualizar"  , "AxVisual", 0, 4},;
                    {"Aut. Frame." , "U_A020AutFrame" , 0, 3},;
                    {"Emerg.Frame.", "U_A020EmeFrame" , 0, 4}}

If ZDP->(FieldPos("ZPD_MSBLQD")) <> 0
	aCores := {	{"!Empty(ZDP->ZPD_MSBLQD)",'BR_PRETO'},; //Hardlock bloqueado
					{"!Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)",'ENABLE' },;		//HardLock Ok
				  	{"Empty(ZDP->ZDP_PROTOC) .And. Empty(ZDP->ZDP_PROAUT) .And. Empty(ZDP->ZPD_MSBLQD)",'DISABLE'}}	//HardLock sem protocolo
Else
	aCores := {	{"!Empty(ZDP->ZDP_MSBLQD)",'BR_PRETO'},; //Hardlock bloqueado
					{"!Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)",'ENABLE' },;		//HardLock Ok
				  	{"Empty(ZDP->ZDP_PROTOC) .And. Empty(ZDP->ZDP_PROAUT) .And. Empty(ZDP->ZDP_MSBLQD)",'DISABLE'}}	//HardLock sem protocolo
EndIf
MBrowse(6/*nT*/,1/*nL*/,22/*nB*/,75/*nR*/,"ZDP"/*cAlias*/,/*aFixe*/,/*cCpo*/,/*nPosI*/,"!Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)"/*cFun*/,/*nDefault*/,aCores/*aColors*/)
Return


User Function CLIA020D()
Local cSvAlias := Alias()

Private cCadastro := FunDesc()
Private aRotina := {{"Pesquisar"		, "AxPesqui"    , 0, 1},;
                    {"Visualizar"		, "U_MA020Visual", 0, 4}}

MBrowse(6,1,22,75,"SA1")

If !Empty(cSvAlias)
	DbSelectArea(cSvAlias)
EndIf
Return

Static Function MyCLIA020()
Local cSvAlias := Alias()
Local aCores   := {}
Private cCadastro := FunDesc()
Private aRotina := {{"Pesquisar"	, "AxPesqui"	 	, 0, 1},;
                    {"A&utorizar"	, "U_A020AutHASP"		, 0, 4},;
                    {"Au&t. Esp."	, "U_A020AutEsp" 		, 0, 3},;
                    {"Visualizar"	, "AxVisual"	 	, 0, 2},;
                    {"Incluir"		, "AxInclui"	 	, 0, 3},;
                    {"Alterar"	 	, "AxAltera"	 	, 0, 4},;
                    {"Excluir"	 	, "AxDeleta"	 	, 0, 5},;
                    {"Protocolo"  	, "U_A020Protoc"	 	, 0, 4},;
                    {"Vincular"   	, "U_A020Vinc"	 	, 0, 4},;
                    {"Histórico"	, "U_A020Log(1)"	, 0, 2}}
//                  {"Aut.E&mail"	, "U_SendLicMail"		, 0, 4}

U_CliServ()
If ZDP->(FieldPos("ZPD_MSBLQD")) <> 0
	aCores := {	{"!Empty(ZDP->ZPD_MSBLQD)",'BR_PRETO'},; //Hardlock bloqueado
					{"!Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)",'ENABLE' },;		//HardLock Ok
				  	{"Empty(ZDP->ZDP_PROTOC) .And. Empty(ZDP->ZDP_PROAUT) .And. Empty(ZDP->ZPD_MSBLQD)",'DISABLE'}}	//HardLock sem protocolo
Else
	aCores := {	{"!Empty(ZDP->ZDP_MSBLQD)",'BR_PRETO'},; //Hardlock bloqueado
					{"!Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)",'ENABLE' },;		//HardLock Ok
				  	{"Empty(ZDP->ZDP_PROTOC) .And. Empty(ZDP->ZDP_PROAUT) .And. Empty(ZDP->ZDP_MSBLQD)",'DISABLE'}}	//HardLock sem protocolo
EndIf
//MBrowse(6/*nT*/,1/*nL*/,22/*nB*/,75/*nR*/,"ZDP"/*cAlias*/,/*aFixe*/,/*cCpo*/,/*nPosI*/,"!Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)"/*cFun*/,/*nDefault*/,aCores/*aColors*/)
If !Empty(cSvAlias)
	DbSelectArea(cSvAlias)
EndIf
// -------------------------------------
// Disconecta RPC para geracao do aponta
// -------------------------------------
VPERPCDisconnect()
Return

User Function A020Vinc(cAlias,nReg,nOpc)
Local lAAprovVin := GetMv("TI_AAPVVIC",,.F.)

If lAAprovVin
	U_THLTA010()
Else
	U_CLVincHL()
EndIf


Return

User Function A020EmpAut(cAlias,nReg,nOpc)
Local i		:= 0

Local oDlg
Local oFont
Local oFontBold
Local oFolder
Local oEnchoice1
Local oBox
Local aGetLicen := {0,0,0,0,0,0,0,0,0,0,0}   //ERP,FOUR,T,LIGHT,IMP,MNT,APS,VEI,PYME, PRODIX, PROTHEUS V
Local aInfo := {}
Local aInfo1
Local aInfo2
Local aBtn := Array(2)
Local aGet := Array(6)
Local aEnchBar := {}
Local nCheckSum := 0
Local nRegCorp := 0
Local nRegEmp := nReg
Local nErrValid := 0
Local cSenhaC := Space(8)
Local cContraSenha := Space(8)
Local cChaveCorp := Space(16)
Local lNewCorp := .F.
Local lLibMan := U_CLLibMan(ZA1->ZA1_CLIENT)

Local dDataValidade := Ctod("")

DbSelectArea("ZA1")
If ZA1->ZA1_CODIGO+ZA1->ZA1_CODFIL =="@@@@" .and. ZA1->ZA1_TIPOEM == "C"
	MsgInfo('Este registro não pode ser autorizado')
	Return
EndIf

ADD FIELD aInfo1 TITULO "Cod.Cliente"      CAMPO "ZA1_CLIENT" TIPO "C" TAMANHO 6 DECIMAL 0 PICTURE "@!" ;
	VALID (ExistCpo("SA1",M->ZA1_CLIENT) .and. InitValidade(@M->ZA1_VALIDA,M->ZA1_CLIENT,@nErrValid) .and. ;
		  U_CLHasCorp(M->ZA1_CLIENT,@nRegCorp,@cChaveCorp,@nCheckSum,@dDataValidade) .and. ;
		  U_RetLicen(M->ZA1_CLIENT,@aGetLicen) .and. U_ValidCorp(nRegCorp,aGet[6]) .and. U_GetRefresh(aGet,@lLibMan));
	OBRIGAT NIVEL 1 WHEN lNewCorp F3 "SA1"
	
ADD FIELD aInfo1 TITULO "Cliente"       CAMPO "ZA1_CLINAM" TIPO "C" TAMANHO 50 DECIMAL 0 NIVEL 1 INITPAD "GetAdvFVal('SA1','A1_NOME',XfILIAL('Sa1')+M->za1_client)"

ADD FIELD aInfo1 TITULO "Data Ref."    CAMPO "ZA1_DTREF"  TIPO "D" TAMANHO 8 DECIMAL 0 PICTURE "@D" OBRIGAT NIVEL 1 INITPAD "dDataBase"

ADD FIELD aInfo1 TITULO "Validade"     CAMPO "ZA1_VALIDA" TIPO "N" TAMANHO 2 DECIMAL 0 PICTURE "99" ;
	VALID M->ZA1_VALIDA > 0 .and. M->ZA1_VALIDA <= 60 OBRIGAT NIVEL 1 INITPAD {|dInit| InitValidade(@dInit,M->ZA1_CLIENT,@nErrValid),dInit} ;
	WHEN nErrValid == 0

ADD FIELD aInfo1 TITULO "Versao"       CAMPO "ZA1_VERSAO" TIPO "C" TAMANHO 1 DECIMAL 0 OBRIGAT NIVEL 1 BOX "1=7.10" INITPAD "'1'"

ADD FIELD aInfo1 TITULO "Solicitante"  CAMPO "ZA1_SOLICI" TIPO "C" TAMANHO 15 DECIMAL 0 NIVEL 1

ADD FIELD aInfo1 TITULO "Contato Siga" CAMPO "ZA1_CONTAT" TIPO "C" TAMANHO 15 DECIMAL 0 NIVEL 1 INITPAD "U_RetContato()"

ADD FIELD aInfo1 TITULO "Cliente Acer" CAMPO "ZA1_ACER"   TIPO "C" TAMANHO 1  DECIMAL 0 NIVEL 1 BOX "S=Sim;N=Nao"

ADD FIELD aInfo1 TITULO "Observacao"   CAMPO "ZA1_OBSERV" TIPO "M" TAMANHO 70 DECIMAL 0 NIVEL 1

For i := 1 To Len(aInfo1)
	cField := aInfo1[i][2]
	If !Empty(aInfo1[i][10])
		If ValType(aInfo1[i][10]) == "B"
			M->&(cField) := Eval(aInfo1[i][10])
		Else
			M->&(cField) := InitPad(aInfo1[i][10])
		EndIf
	Else
		M->&(cField) := (cAlias)->&(cField)
	EndIf
	Aadd(aInfo,cField)
Next
M->ZA1_CONTAT := U_RetContato()

ADD FIELD aInfo2 TITULO "Cod Empresa"  CAMPO "ZA1_CODIGO" TIPO "C" TAMANHO 2  DECIMAL 0 PICTURE "@!" VALID U_ValidEmp(M->ZA1_CODIGO,M->ZA1_CODFIL,M->ZA1_NOMEEM,M->ZA1_NOMEFI) OBRIGAT NIVEL 1

ADD FIELD aInfo2 TITULO "Cod Filial"   CAMPO "ZA1_CODFIL" TIPO "C" TAMANHO 2  DECIMAL 0 PICTURE "@!" VALID U_ValidEmp(M->ZA1_CODIGO,M->ZA1_CODFIL,M->ZA1_NOMEEM,M->ZA1_NOMEFI) OBRIGAT NIVEL 1

ADD FIELD aInfo2 TITULO "Nome Empresa" CAMPO "ZA1_NOMEEM" TIPO "C" TAMANHO 15 DECIMAL 0 PICTURE "@X" OBRIGAT NIVEL 1

ADD FIELD aInfo2 TITULO "Nome Filial"  CAMPO "ZA1_NOMEFI" TIPO "C" TAMANHO 15 DECIMAL 0 PICTURE "@X" OBRIGAT NIVEL 1

For i := 1 To Len(aInfo2)
	cField := aInfo2[i][2]
	M->&(cField) := (cAlias)->&(cField)
	Aadd(aInfo,cField)
Next

//inicializa chave corporativa
U_CLHasCorp(M->ZA1_CLIENT,@nRegCorp,@cChaveCorp,@nCheckSum,@dDataValidade)

oFont     := TFont():New('Monoas',0,-12)
oFontBold := TFont():New('Monoas',0,-12,,.T.)

DEFINE MSDIALOG oDlg TITLE "Autorização" FROM 40,30 TO 450,600 PIXEL

oEnchoice1 := Msmget():New("ZA1",nReg,4,,,,,{13,02,78,__DlgWidth(oDlg)},,,,,,oDlg,,,.F.,,,.T.,aInfo1)

@80,02 FOLDER oFolder PROMPT "Liberação" PIXEL SIZE __DlgWidth(oDlg)-2,__DlgHeight(oDlg)-80

@03,07 SAY "Autorização Corporativa" PIXEL OF oFolder:aDialogs[1] FONT oFontBold
@13,05 SCROLLBOX oBox SIZE 95,130 OF oFolder:aDialogs[1]

@02,02 SAY "Chave Corporativa" PIXEL OF oBox SIZE 100,13 FONT oFont 
@02,62 GET aGet[1] VAR cChaveCorp PIXEL OF oBox SIZE 60,05 PICTURE "@!" RIGHT READONLY

@14,02 SAY "Check-Sum" PIXEL OF oBox SIZE 100,13 FONT oFont
@14,62 GET aGet[2] VAR nCheckSum PIXEL OF oBox SIZE 60,05 PICTURE "99999999" RIGHT READONLY

@26,02 SAY "Data de Validade" PIXEL OF oBox SIZE 100,13 FONT oFont
@26,62 GET aGet[3] VAR dDataValidade PIXEL OF oBox SIZE 60,05 RIGHT READONLY

@60,02 SAY aGet[6] VAR "Autorização Corporativa não calculada." PIXEL SIZE 120,13 FONT oFontBold OF oBox
U_ValidCorp(nRegCorp,aGet[6])

DEFINE SBUTTON aBtn[1] TYPE 1 FROM 40,92 ENABLE OF oBox ONSTOP "Calcular Corporativa - <F5>" ;
ACTION If(Obrigatorio(oEnchoice1:aGets,oEnchoice1:aTela) .and. nErrValid > -2,;
		 (dDataValidade := M->ZA1_DTREF+(M->ZA1_VALIDA*30),;
		 CLCalcCorp(aGetLicen,@cChaveCorp,@nCheckSum,@dDataValidade),;
		 U_CLGrvCorp(aGetLicen,cChaveCorp,nCheckSum,dDataValidade,M->ZA1_DTREF,@nRegCorp,M->ZA1_CLIENT,M->ZA1_VERSAO,,M->ZA1_OBSERV),;
		 U_ValidCorp(nRegCorp,aGet[6]),;
		 U_GetRefresh(aGet)),)

@03,147 SAY "Autorização Empresa" PIXEL OF oFolder:aDialogs[1] FONT oFontBold
oEnchoice2 := Msmget():New("ZA1",nReg,4,,,,,{13,145,108,275}  ,,,,,,oFolder:aDialogs[1],,,.T.,,,.T.,aInfo2)

@oEnchoice2:EnchTop() + 10,02 SAY "Senha" OF oEnchoice2:oBox PIXEL
@oEnchoice2:EnchTop() + 10,38 GET aGet[4] VAR cSenhaC OF oEnchoice2:oBox PIXEL PICTURE "@!" SIZE 30,5
												 
@oEnchoice2:EnchTop() + 22,02 SAY "Contra-Senha" OF oEnchoice2:oBox PIXEL
@oEnchoice2:EnchTop() + 22,38 GET aGet[5] VAR cContraSenha OF oEnchoice2:oBox PIXEL PICTURE "@!" SIZE 30,5 READONLY

DEFINE SBUTTON aBtn[2] TYPE 1 FROM oEnchoice2:EnchTop() + 20,73 ENABLE OF oEnchoice2:oBox ONSTOP "Calcular Empresa - <F6>" ;
ACTION If(Obrigatorio(oEnchoice2:aGets,oEnchoice2:aTela) .and. nErrValid > -2,;
		 (If(U_CLValidPsw(M->ZA1_CODIGO,M->ZA1_CODFIL,M->ZA1_NOMEEM,M->ZA1_NOMEFI,@cSenhaC,@cContraSenha,nCheckSum),;
		 (nRegEmp := U_CLGrvEmp1(cSenhaC,cContraSenha,dDataValidade,aInfo)),),;
		 U_GetRefresh(aGet)),)

Aadd(aEnchBar,{"BMPVISUAL" ,{|| U_CLViewCli(M->ZA1_CLIENT)},"Visualizar Cliente","Visualizar"})
Aadd(aEnchBar,{"BMPINCLUIR",{|| nErrValid := 0,U_NewInfo(aInfo1,aInfo2),lNewCorp := .T.,lNewEmp := .T.,;
								U_CLHasCorp(M->ZA1_CLIENT,@nRegCorp,@cChaveCorp,@nCheckSum,@dDataValidade,@aGetLicen,.T.),;
								cContraSenha := Space(8),U_ValidCorp(nRegCorp,aGet[6]),U_GetRefresh(aGet),;
								oEnchoice1:EnchRefreshAll(),oEnchoice1:EnchRefreshAll()},"Novo Cliente - <F7>","Novo"})
Aadd(aEnchBar,{"PEDIDO"    ,{|| InitValidade(@M->ZA1_VALIDA,M->ZA1_CLIENT,@nErrValid)},"Verificar Validade - <F8>","Validade"})
Aadd(aEnchBar,{"PRINT02"   ,{|| If(nErrValid > -2,U_CLPrint(.T.,nRegEmp,"CLIR020"),)},"Imprimir Autorização - <F9>","Imprimir"})
ACTIVATE MSDIALOG oDlg CENTERED ON INIT EnchoiceBar(oDlg,{|| oDlg:End()},{|| oDlg:End()},,aEnchBar)

DbSelectArea("ZA1")
DbGoTo(nReg)
Return

Static Function InitValidade(nValidade,cClient,nErrValid)

nValidade := U_VPEGetValid(cClient,@nErrValid)

If nErrValid == 3	//3 meses de validade para o Mexico
	nValidade := 3
ElseIf nErrValid == -2 .And. nValidade > 0
	nErrValid := 0
EndIf
Return .T.

User Function A020Create(cAlias,nReg,nOpc)
Local i		:= 0
Local oDlg
Local oEnchoice
Local aInfo1
Local aGetLicen := {0,0,0,0,0,0,0,0,0,0,0}
Local nErrValid := 0

Local cTipo

ADD FIELD aInfo1 TITULO "Arquivo"   CAMPO "cArquivo"   TIPO "C" TAMANHO 100 DECIMAL 0 PICTURE "@!" OBRIGAT NIVEL 1

ADD FIELD aInfo1 TITULO "Cliente"   CAMPO "ZA1_CLIENT" TIPO "C" TAMANHO 6   DECIMAL 0 PICTURE "@!" ;
	VALID ExistCpo("SA1",M->ZA1_CLIENT) .and. InitValidade(@M->ZA1_VALIDA,M->ZA1_CLIENT,@nErrValid) ;
	OBRIGAT NIVEL 1 F3 "SA1"

ADD FIELD aInfo1 TITULO "Data Ref." CAMPO "ZA1_DTREF"  TIPO "D" TAMANHO 8   DECIMAL 0 PICTURE "@D" OBRIGAT NIVEL 1 INITPAD "dDataBase"

ADD FIELD aInfo1 TITULO "Validade"  CAMPO "ZA1_VALIDA" TIPO "N" TAMANHO 2   DECIMAL 0 PICTURE "99" ;
	VALID M->ZA1_VALIDA > 0 .and. M->ZA1_VALIDA <= 60 OBRIGAT NIVEL 1 WHEN nErrValid == 0

ADD FIELD aInfo1 TITULO "Versao"    CAMPO "ZA1_VERSAO" TIPO "C" TAMANHO 1   DECIMAL 0 OBRIGAT NIVEL 1 BOX "1=7.10" INITPAD "'1'"

ADD FIELD aInfo1 TITULO "Observacao" CAMPO "ZA1_OBSERV" TIPO "M" TAMANHO 70 DECIMAL 0 NIVEL 1

For i := 1 To Len(aInfo1)
	cField := aInfo1[i][2]
	cTipo := aInfo1[i][3]
	If !Empty(aInfo1[i][10])
		M->&(cField) := InitPad(aInfo1[i][10])
	Else
		If cTipo $ "CM"
			M->&(cField) := Space(aInfo1[i][4])
		ElseIf cTipo == "N"
			M->&(cField) := 0
		ElseIf cTipo == "D"
			M->&(cField) := Ctod("")
		ElseIf cTipo == "L"
			M->&(cField) := .F.
		EndIf
	EndIf
Next

DEFINE MSDIALOG oDlg TITLE "Autorização Empresa" FROM 00,00 TO 600,600 PIXEL

oEnchoice := Msmget():New("ZA1",nReg,4,,,,,{13,02,100,300},,,,,,oDlg,,,.F.,,,.T.,aInfo1)
oEnchoice:oBox:Align := CONTROL_ALIGN_ALLCLIENT

ACTIVATE MSDIALOG oDlg CENTERED ;
ON INIT EnchoiceBar(oDlg,{|| If(Obrigatorio(oEnchoice:aGets,oEnchoice:aTela) .and. nErrValid > -2,U_CLRpcAut(cArquivo,M->ZA1_CLIENT,M->ZA1_DTREF,M->ZA1_VALIDA,M->ZA1_VERSAO,aGetLicen,M->ZA1_OBSERV),)},;
		{|| oDlg:End()},,;
		{{"SALVAR",{|| cArquivo := cGetFile("Arquivo Emp | *.Emp |","Procurar",,,.T.,/*GETF_ONLYSERVER*/)},"Procurar Arquivo","Arquivo"}})

Return

User Function A020ChkDisk()
Local cFile
Local cSvAlias := Alias()
Local oDlg
Local oPanel
Local oGroup
Local oFontBold
Local oFont
Local aGetLicen
Local aGet
Local cChaveCorp := Space(16)
Local dDataValidade := Ctod("")
Local nCheckSum := 0
Local aBitmap
Local aEmpr := {}
Local cIndice
Local oEmpr
Local cRealFile

cRealFile := cGetFile("Arquivo Emp | *.Emp |","Procurar",,,.T.,/*GETF_ONLYSERVER*/)

If !File(cRealFile)
	ApMsgStop("Arquivo não encontrado")
	Return
EndIf

cFile := U_File2Server(cRealFile)

If Empty(cFile) .or. !U_CLOpenEmp(cFile,,"SIGACLI")	
	ApMsgStop("Não foi possível abrir o arquivo")
	Return
EndIf

DbSelectArea("SIGACLI")
If FieldPos("M0_LICENSA") == 0
	DbCloseArea()
	MsgInfo("Arquivo da versao antiga")
Else
	aGet := Array(3)
	aGetLicen := Array(Len(U_SayLicen()))
	Afill(aGetLicen,0)
	aBitmap := {LoadBitmap(GetResources(),"BR_VERDE"),LoadBitmap(GetResources(),"BR_VERMELHO")}
	
	cIndice := U_CliGetDir(cFile) + CriaTrab(,.F.)
	INDEX ON M0_CODIGO+M0_CODFIL TO &(cIndice)
	
	DbGoTop()
	While !Eof()
		If Empty(cChaveCorp) .and. P_CheckSumIsOk(M0_LICENSA,M0_CORPKEY,M0_CHKSUM,M0_DTVLD,,"SIGACLI")
			cChaveCorp    := M0_CORPKEY
			nCheckSum     := M0_CHKSUM
			dDataValidade := M0_DTVLD
			aGetLicen     := CLCalcLic(M0_LICENSA)
			Exit
		EndIf
		DbSkip()
	End
	
	DbGoTop()
	While !Eof()
		Aadd(aEmpr,{.F.,M0_CODIGO,M0_CODFIL,M0_NOME,M0_FILIAL,M0_PSW,M0_CTPSW,M0_CGC})
		If PSWEmpOK(M0_CODIGO,M0_CODFIL,M0_NOME,M0_FILIAL,M0_PSW,M0_CTPSW,nCheckSum,,"SIGACLI")
			aEmpr[Len(aEmpr)][1] := .T.
		EndIf
		DbSkip()
	End
	
	DbGoTop()
	
	oFont     := TFont():New('Monoas',0,-12)
	oFontBold := TFont():New('Monoas',0,-12,,.T.)
	
	DEFINE MSDIALOG oDlg TITLE "Verificar Arquivo - "+Upper(cRealFile) FROM 40,30 TO 430,600 PIXEL
	
	@13,01 MSPANEL oPanel PROMPT "" SIZE __DlgWidth(oDlg)-1,117
	
	@03,05 SAY "Autorização Corporativa" PIXEL OF oPanel FONT oFontBold
	@13,03 SCROLLBOX oGroup SIZE 45,__DlgWidth(oPanel)-3 OF oPanel
	
	@02,05 SAY "Chave Corporativa" PIXEL OF oGroup SIZE 100,13 FONT oFont 
	@02,65 GET aGet[1] VAR cChaveCorp PIXEL OF oGroup SIZE 60,05 PICTURE "@!" RIGHT WHEN .F.
	
	@14,05 SAY "Check-Sum" PIXEL OF oGroup SIZE 100,13 FONT oFont
	@14,65 GET aGet[2] VAR nCheckSum PIXEL OF oGroup SIZE 60,05 PICTURE "99999999" RIGHT WHEN .F.
	
	@26,05 SAY "Data de Validade" PIXEL OF oGroup SIZE 100,13 FONT oFont
	@26,65 GET aGet[3] VAR dDataValidade PIXEL OF oGroup SIZE 60,05 RIGHT WHEN .F.
	
	@75,04 LISTBOX oEmpr FIELDS HEADER "","Empresa","Filial","Nome Empresa","Nome Filial","CNPJ","Senha","Contra-Senha" ;
	SIZE __DlgWidth(oDlg)-6,__DlgHeight(oDlg)-78 PIXEL
	
	oEmpr:SetArray(aEmpr)
	oEmpr:bLine := {|x| x := oEmpr:nAt, {aBitmap[If(aEmpr[x][1],1,2)],aEmpr[x][2],aEmpr[x][3],aEmpr[x][4],aEmpr[x][5],Transform(aEmpr[x][8],"99.999/9999-99"),aEmpr[x][6],aEmpr[x][7]}}
	
	ACTIVATE MSDIALOG oDlg CENTERED ON INIT EnchoiceBar(oDlg,{|| oDlg:End()},{|| oDlg:End()})
	
	DbSelectArea("SIGACLI")
	DbCloseArea()
	Ferase(cFile)
	Ferase(cIndice+RetIndExt())
EndIf

If !Empty(cSvAlias)
	DbSelectArea(cSvAlias)
EndIf
Return

User Function A020AutEsp(cAlias,nReg,nOpc)
Local cCliEsp
Local aItensCDU 			:= {}
Local aItensSMS 			:= {}
Local aLicReadItemsSD6
Local lGetCanceledItems		:= MsgYesNo("Considerar os itens de contratos com o status cancelado para qualquer versionamento de contrato?","Atenção")
local aAreaSA1				:= SA1->(getArea())
local aAreaZDP				:= ZDP->(getArea())

cCliEsp := U_CliCodCli()

SA1->(dbSetOrder(1))
SA1->(dbSeek(xFilial('SA1')+cCliEsp))
ZDP->(dbSetOrder(1))
ZDP->(dbSeek(xFilial('ZDP')+cCliEsp))

If !Empty(cCliEsp)  
	U_VPEIsNewModel( cCliEsp, aItensCDU, aItensSMS,,lGetCanceledItems)
	InfoContrato(cCliEsp, aItensCDU, aItensSMS,.T.,.T.)
EndIf

// ------------------------------------------                    		
// Verifica se é uma liberação Série 1(First)
// ------------------------------------------
If ( VPEIsRMFirst() )
	
	// -----------------------
	// Gera aponta do RM First
	// -----------------------
	If ( VPEBuildOldAponta( cCliEsp, {}) )
		VPEBuildFirstLic(cCliEsp)
	EndIf
	
	VPESendMailAuthorization( cCliEsp )
Else

	// ----------------------------------------------
	// Converte itens de contrato em Slots de liceças   
	// ----------------------------------------------
	aLicReadItemsSD6 := U_VPEAutHardlock( cCliEsp, aItensCDU, aItensSMS )

	// -------------------------------------------------
	// Carrega a interface de liberação da nova politica
	// -------------------------------------------------
	U_VPEUIAuthorization(,,, aLicReadItemsSD6,,.T.,cCliEsp)
EndIf
restArea(aAreaZDP)
restArea(aAreaSA1)
Return

User Function A020AutFrame(cAlias,nReg,nOpc)
Local cCliEsp
Local aItensCDU 		:= {}
Local aItensSMS 		:= {}
Local aCNPJ          := {}
Local aLicReadItemsSD6
Local lGetCanceledItems := .F.

cCliEsp := ZDP->ZDP_CLIENT

If !Empty(cCliEsp)  
	U_VPEIsNewModel( cCliEsp, aItensCDU, aItensSMS,aCNPJ,lGetCanceledItems)

	memowrit("c:\"+cCliEsp+"CDU"+".html",VarInfo("aItensCDU",aItensCDU,/*nivel*/,.T.,.F.))
	memowrit("c:\"+cCliEsp+"SMS"+".html",VarInfo("aItensSMS",aItensSMS,/*nivel*/,.T.,.F.))
	memowrit("c:\"+cCliEsp+"CNPJ"+".html",VarInfo("aCNPJ",aCNPJ,/*nivel*/,.T.,.F.))
	
	If ( !VPEIsSERIE1() .And. !VPEIsRMFirst() )
		lNewModalidade := InfoContrato(cCliEsp, aItensCDU, aItensSMS,.F.,.T.)
	Else
		lNewModalidade := .T.
	EndIf
Else
	lNewModalidade := .F.
EndIf
If ( lNewModalidade )  
	// ------------------------------------------                    		
	// Verifica se é uma liberação Série 1(First)
	// ------------------------------------------
	If ( VPEIsSERIE1() .Or. VPEIsRMFirst() )
		
		If ( VPEIsRMFirst() )
			// -----------------------
			// Gera aponta do RM First
			// -----------------------
			If ( VPEBuildOldAponta( cCliEsp, {}) )
				VPEBuildFirstLic(cCliEsp)
			EndIf
		Else
			// ------------------------------
			// Gera aponta do Série 1 (First)
			// ------------------------------
			If ( U_VPEGetValid( cCliEsp ) > 0 )
				VPESerie1Aponta( cCliEsp, VPEGetSERIE1())
				VPEBuildFirstLic(cCliEsp)
			EndIf
		EndIf

		VPESendMailAuthorization( cCliEsp )
	Else
	
		// ----------------------------------------------
		// Converte itens de contrato em Slots de liceças   
		// ----------------------------------------------
		aLicReadItemsSD6 := U_VPEAutHardlock( cCliEsp, aItensCDU, aItensSMS )
	
		// -------------------------------------------------
		// Carrega a interface de liberação da nova politica
		// -------------------------------------------------
		U_VPEUIAuthorization(,aCNPJ,, aLicReadItemsSD6,5/30,.F.,cCliEsp,.T.) //Não deixa mudar o contrato.
	EndIf	
EndIf
Return

User Function A020EmeFrame()

Local cEmerg     := ""
Local cErro      := ""
Local cHTML      := ""
Local cCliEsp    := ZDP->ZDP_CLIENT
            
//--------------------------------------------------
// Posiciona no cadastro de cliente
//--------------------------------------------------
dbSelectArea("SA1")
DbSetOrder(1)
MsSeek(xFilial()+cCliEsp)
U___BuildNewEm(cCliEsp,SA1->A1_CGC,Date(),@cEmerg,@cErro,@cHtml)
If !Empty(cErro)
	MsgStop(cErro)	
Else
	MemoWrit("c:\emergency.key",cEmerg)
	MemoWrit("c:\leiame.htm",cHTML)
	MsgInfo("Chave gerada e disponível em: c:\emergency.key")
EndIf

Return(.T.)


User Function A020Protoc()
Local oDlg
Local aInfo := {}

ADD FIELD aInfo TITULO "Cliente" CAMPO "ZDP_CLIENT" TIPO "C" TAMANHO 6  DECIMAL 0 PICTURE "@!" OBRIGAT NIVEL 1
ADD FIELD aInfo TITULO "ID"		 CAMPO "ZDP_ID"		TIPO "C" TAMANHO 15 DECIMAL 0 OBRIGAT NIVEL 1
M->ZDP_CLIENT := ZDP->ZDP_CLIENT
M->ZDP_ID := ZDP->ZDP_ID

DEFINE MSDIALOG oDlg TITLE "Recebimento" FROM 0,0 TO 200,350 PIXEL

Msmget():New("ZDP",0,3,,,,,{13,00,__DlgHeight(oDlg),__DlgWidth(oDlg)},,,,,,oDlg,,,.T.,,,.T.,aInfo)

ACTIVATE MSDIALOG oDlg CENTER ON INIT EnchoiceBar(oDlg,{|| If(A020ProtOk(),oDlg:End(),)},{|| oDlg:End()})

Return

Static Function A020ProtOk()
Local nIdxOrd
Local nRecno
Local lRet := .F.

DbSelectArea("ZDP")
nIdxOrd := IndexOrd()
nRecno := Recno()
DbSetOrder(1)
If DbSeek(xFilial() + M->ZDP_CLIENT + M->ZDP_ID)
	If Empty(ZDP->ZDP_PROTOC) .and. Empty(ZDP->ZDP_PROAUT)
		If U_CLSetVer() <> ZDP->ZDP_PROVER
			ApMsgStop("Protocolo gerado para versão " + ZDP->ZDP_PROVER)
		Else
			lRet := .T.
			RecLock("ZDP",.F.)
			ZDP->ZDP_PROAUT := MsDate()
			MsUnlock()
		EndIf
	Else
		MsgInfo("Protocolo já registrado")
	EndIf
Else
	ApMsgStop("HardLock nao encontrado")
EndIf

DbSetOrder(nIdxOrd)
DbGoTo(nRecno)
Return lRet

// -------------------------------------------

User Function A020AutHASP(cAlias,nReg,nOpc,cCliEsp,lAsk)
Local aItensCDU 		:= {}
Local aItensSMS 		:= {}
Local aCNPJ				:= {}
Local aLicReadItemsSD6
Local aHASP
Local aLoadSV
Local aSegSerie      := {}
Local nSvRecnoZDP       := ZDP->(Recno())
Local nSvOrderZDP       := ZDP->(IndexOrd())
Local nMeses
Local nError
Local cMessage
Local cCliente			:= ZDP->ZDP_CLIENT
Local lUnidade       := .F.
Local lmeses		  := GetMv("TI_L12MESS",,.T.) //Habilita funcionalidade para setar 12 meses para clientes que tem somente CDU ativa
local lBloqVal := .f.

// ---------------------------------------------------------
// Verifica se o hardlock é de uma unidade TOTVS
// ---------------------------------------------------------
If nReg == 0
	cCliente := cCliEsp
EndIf
dbSelectArea("SA1")
dbSetOrder(1)
If MsSeek(xFilial("SA1")+cCliente)
	DbSelectArea("SD6")         //Olhar Manutencoes
	DbSetOrder(1)
	If .T. .Or. !DbSeek(xFilial()+SA1->A1_COD,.T.)
		dbSelectArea("ADK")
		dbSetOrder(3)
		If MsSeek(xFilial("ADK")+SA1->A1_CGC)
			While ADK->(!Eof()) .And. xFilial("ADK")==ADK->ADK_FILIAL .And. SA1->A1_CGC == ADK->ADK_CNPJ
			    If RegistroOk("ADK",.F.) .And. AllTrim(ADK->ADK_TIPO) == "4"
					lUnidade := .T.
				EndIf
				ADK->(dbSkip())
			End   
			If  !lUnidade
				MsgStop("Os registros estão bloqueados na tabela ADK!")
			EndIf
		EndIf
	EndIf	
EndIf

If nReg <> 0
	U_VPEIsNewModel( ZDP->ZDP_CLIENT, aItensCDU, aItensSMS, aCNPJ )
	If ( !VPEIsRMFirst() )
		InfoContrato(ZDP->ZDP_CLIENT, aItensCDU, aItensSMS, lAsk,.F.)
	EndIf
Else
	cCliente := cCliEsp
EndIf

	aHASP := {}
	
	ZDP->(dbSetOrder(1))
	ZDP->(dbSeek(xFilial("ZDP")+cCliente))
	While ZDP->(!Eof()) .And. ZDP->ZDP_FILIAL == xFilial("ZDP") .And. ZDP->ZDP_CLIENT == cCliente

		// -------------------------------------
		// Verifica se o hardlock esta bloqueado		
		// -------------------------------------		
		If (ZDP->(FieldPos("ZDP_MSBLQD")) <> 0 .And. Empty(ZDP->ZDP_MSBLQD))			
			If (ZDP->(FieldPos("ZDP_SMSVER")) == 0 .Or. ZDP->ZDP_SMSVER<>"00")
				If ( !Empty(ZDP->ZDP_VPELIC)  )
					aLoadSV := Str2Array(ZDP->ZDP_VPELIC)
				Else
					aLoadSV := {{},{},{}}
				EndIf
				VPESincSlots(@aLoadSV)
				Aadd( aHASP, { .T., IIf(Empty(ZDP->ZDP_HASHID),Trim(ZDP->ZDP_ID),ZDP->ZDP_HASHID), ZDP->ZDP_VENCTO, ZDP->ZDP_CHKSUM, aClone(aLoadSV[1]), aClone(aLoadSV[2]),,,ZDP->(Recno())})
				If Ascan( aSegSerie, {|x| x[1] == 7003}) == 0 .And. !Empty(ZDP->ZDP_SMSVER)
					Aadd( aSegSerie, { 7003, 09+Val(ZDP->ZDP_SMSVER) })
				EndIf				
			EndIf
		EndIf
		ZDP->(dbSkip())
	EndDo
	
	ZDP->(dbSetOrder(nSvOrderZDP))
	ZDP->(dbGoto(nSvRecnoZDP))
	// ----------------------------------------------
	// Verifica se há hardlock válido
	// ----------------------------------------------
	If !Empty(aHasp)	
		If lUnidade
			aLicReadItemsSD6 := {}
			aadd(aLicReadItemsSD6,{4000,200,.F.})
			aadd(aLicReadItemsSD6,{4001,200,.F.})
			aadd(aLicReadItemsSD6,{4002,200,.F.})
			aadd(aLicReadItemsSD6,{4003,200,.F.})
			aadd(aLicReadItemsSD6,{4005,008,.F.})
			aadd(aLicReadItemsSD6,{4006,001,.F.})
			aadd(aLicReadItemsSD6,{4009,008,.F.})
			aadd(aLicReadItemsSD6,{4012,1000,.F.})
			aadd(aLicReadItemsSD6,{4091,018,.F.})
			
			aadd(aLicReadItemsSD6,{4107,200,.F.})
			aadd(aLicReadItemsSD6,{4143,200,.F.})
			aadd(aLicReadItemsSD6,{4144,200,.F.})
			aadd(aLicReadItemsSD6,{4147,200,.F.})
			aadd(aLicReadItemsSD6,{4156,200,.F.})
			aadd(aLicReadItemsSD6,{4160,200,.F.})
			aadd(aLicReadItemsSD6,{4161,200,.F.})

			aadd(aLicReadItemsSD6,{4096,200,.F.})
			aadd(aLicReadItemsSD6,{3017,200,.F.})
			aadd(aLicReadItemsSD6,{3033,200,.F.})
			aadd(aLicReadItemsSD6,{3011,200,.F.})
			aadd(aLicReadItemsSD6,{3019,200,.F.})
			aadd(aLicReadItemsSD6,{3051,200,.F.})
			aadd(aLicReadItemsSD6,{3500,200,.F.})
			
			aadd(aLicReadItemsSD6,{3125,001,.F.})
			
			nMeses := DateDiffDay(MonthSum(dDataBase, 12), dDataBase)//12		
		Else
			// ----------------------------------------------
			// Converte itens de contrato em Slots de liceças   
			// ----------------------------------------------
			aLicReadItemsSD6 := U_VPEAutHardlock( ZDP->ZDP_CLIENT, aItensCDU, aItensSMS )	
			
			//Verifica se possui somente CDU ativa e parâmetro esteja habilitado para a funcionalidade
			If lmeses .AND. Len(aItensCDU) > 0 .AND. Empty(aItensSMS)
				nMeses := DateDiffDay(MonthSum(dDataBase, 12), dDataBase)//12
				lBloqVal := .t.				
			Else
				// --------------------------------
				// Verifico a validade da liberação
				// --------------------------------
				AI0->(DbSetOrder(1))
				AI0->(DbSeek(xFilial("AI0")+cCliente))
				If  AI0->AI0_SETPUB == "1"
					nMeses := DateDiffDay(MonthSum(dDataBase, 12), dDataBase)//12
					lBloqVal := .t.
				Else 
					nMeses := DateDiffDay(MonthSum(dDataBase, 4), dDataBase)//4
					lBloqVal := .t.
				Endif
				nMeses := U_VPEGetValid( cCliente, @nError, .T., @cMessage,ZDP->(FieldPos("ZDP_HASHTP"))<>0 .And. Trim(ZDP->ZDP_HASHTP)$"1|2|3")
			EndIf
			
		EndIf
		// -------------------------------------------------
		// Carrega a interface de liberação da nova politica 
		// -------------------------------------------------
		U_VPEUIAuthorization(aHASP, aCNPJ, aSegSerie, aLicReadItemsSD6, nMeses, .F., , ,lBloqVal)
	Else
		ApMsgStop("Cliente não possui hardlock apto a receber licença. Verifique o status de bloqueio dos hardlocks!")
	EndIf
	
Return

// -------------------------------------------

User Function A020OldAutHasp(cAlias,nReg,nOpc)
Local oDlg
Local oPanel1
Local oPanel2
Local oFont := TFont():New("MS Sans Serif",0,-7)
Local oFontBold := TFont():New("MS Sans Serif",0,-12,,.T.)
Local oHASP
Local oFolder
Local oNo := LoadBitmap(GetResources(), "LBNO")
Local oOk := LoadBitmap(GetResources(), "LBOK")
Local oEnchoice
Local oCNPJ

Local lEsp := .F.
Local lShow := GetPvProfString(cUserName,"Show","N",U_CLIniFile()) == "S"

Local cDriver := GetPvProfString(cUserName,"Driver","",U_CLIniFile())

Local ni,nj
Local nZDPRecno
Local nZDPOrder
Local nLenLic
Local nLenLight
Local nErrValid := 0
Local nWndTop
Local nWndLeft
Local nAdjust
Local nWidth
Local nHeight
Local nCNPJ := 1

Local aBox := Array(4)
Local aHASP := {}
Local aMemoLic
Local aEnchBar := {}
Local aInfo1
Local aCNPJ

Local aSayLic
Local aObjLic
Local aGetLic

Local aSayLight
Local aObjLight
Local aGetLight

Local aObjFld1
Local aObjFld2

LOCAL NX

Local aColigada := {}
Local aObjBtnIfno := {}
Local oColig
Local aRetTree := {}
Local oPanel
Local oBaseForm := FWUIBaseForm():New()
Local oSay
local nZDPVALO	:= 0

ADD FIELD aInfo1 TITULO "Cliente"      CAMPO "ZDP_CLIENT" TIPO "C" TAMANHO 6 DECIMAL 0 PICTURE "@!" OBRIGAT NIVEL 1 WHEN .F. F3 "SA1"

ADD FIELD aInfo1 TITULO "Data Ref."    CAMPO "ZDP_DTREF"  TIPO "D" TAMANHO 8 DECIMAL 0 PICTURE "@D" OBRIGAT NIVEL 1

ADD FIELD aInfo1 TITULO "Validade"		CAMPO "ZDP_VALIDA" TIPO "N" TAMANHO 3 DECIMAL 0 PICTURE "999";
											VALID {|| U_ZDPVALID(@M->ZDP_VALIDA, @M->ZDP_VENCTO, nZDPVALO), nZDPVALO := M->ZDP_VALIDA, oEnchoice:refresh()} ;
											OBRIGAT NIVEL 1 

ADD FIELD aInfo1 TITULO "Solicitante"  CAMPO "ZDP_SOLICI" TIPO "C" TAMANHO 15 DECIMAL 0 NIVEL 1

ADD FIELD aInfo1 TITULO "Contato Siga" CAMPO "ZDP_CONTAT" TIPO "C" TAMANHO 15 DECIMAL 0 NIVEL 1

ADD FIELD aInfo1 TITULO "Observacao"   CAMPO "ZDP_OBSERV" TIPO "M" TAMANHO 70 DECIMAL 0 NIVEL 1

If Type("M->ZDP_CLIENT") == "U"
	M->ZDP_CLIENT := ZDP->ZDP_CLIENT
	M->ZDP_DTREF  := dDataBase
	M->ZDP_VALIDA := U_VPEGetValid(ZDP->ZDP_CLIENT,@nErrValid)
	M->ZDP_SOLICI := ZDP->ZDP_SOLICI
	M->ZDP_CONTAT := Padr(U_RetContato(),15)
	M->ZDP_OBSERV := ZDP->ZDP_OBSERV
	
	DbSelectArea("SA1")
	DbSetOrder(1)
	DbSeek(xFilial()+M->ZDP_CLIENT)
Else
	M->ZDP_OBSERV := ZDP->ZDP_OBSERV
EndIf

If Trim(M->ZDP_CLIENT) == "99999"
	lEsp := .T.
	M->ZDP_VALIDA := DateDiffDay(MonthSum(dDataBase, 6), dDataBase)//6
	M->ZDP_CONTAT := Padr(U_RetContato(),15)
EndIf

//verifica se o protocolo foi recebido
If !lEsp
	/*If Empty(ZDP->ZDP_PROTOC) .and. Empty(ZDP->ZDP_PROAUT)
		MsgStop("O Protocolo de Recebimento ainda não foi registrado")
		Return
	Else
	*/
	If !U_ValVincHL(M->ZDP_CLIENT) .or. !U_ValSitCli(M->ZDP_CLIENT)
		Return
	EndIf
EndIf

aSayLic := U_Licen710()
nLenLic := Len(aSayLic)
aObjLic := Array(nLenLic)
aObjFld1 := Array(nLenLic)

aSayLight := U_Light710()
nLenLight := Len(aSayLight)
aObjLight := Array(nLenLight)
aObjFld2 := Array(nLenLight)

U_HLLicenses(M->ZDP_CLIENT,@aGetLic,@aGetLight,@aCNPJ,@aRetTree)

DbSelectArea("ZDP")
nZDPOrder := IndexOrd()
nZDPRecno := Recno()
If lEsp
	aHASP := {{.T.,Space(15),Ctod(""),0,Array(nLenLic),Array(nLenLight),{},{}}}
	AFill(aHASP[1][5],0)
	AFill(aHASP[1][6],0)
Else
	DbSetOrder(1)
	DbSeek(xFilial() + M->ZDP_CLIENT)
	ni := 0
	While !Eof() .and. M->ZDP_CLIENT == ZDP->ZDP_CLIENT
		// -------------------------------------
		// Verifica se o hardlock esta bloqueado
		// -------------------------------------
		If (ZDP->(FieldPos("ZDP_MSBLQD")) <> 0 .And. Empty(ZDP->ZDP_MSBLQD)) 
			ni++
			Aadd(aHASP,{.T.,ZDP->ZDP_ID,ZDP->ZDP_VENCTO,ZDP->ZDP_CHKSUM,{/*lic genericas*/},{/*lic light*/},{/*CNPJ*/},{/*Coligadas*/}})
		
			aHASP[ni][5] := Array(nLenLic)
			aHASP[ni][6] := Array(nLenLight)
			AFill(aHASP[ni][5],0)
			AFill(aHASP[ni][6],0)
		
			If Empty(ZDP->ZDP_LICENS)
				If aHASP[ni][1]
					aHASP[ni][5] := AClone(aGetLic)
					aHASP[ni][6] := AClone(aGetLight)
				EndIf
			Else
				aMemoLic := Str2Array(ZDP->ZDP_LICENS)
				For nj := 1 To Len(aMemoLic[1])
					aHASP[ni][5][nj] := aMemoLic[1][nj]
				Next
		
				For nj := 1 To Len(aMemoLic[2])
					aHASP[ni][6][nj] := aMemoLic[2][nj]
				Next
			EndIf
		EndIf
		DbSkip()
	EndDo
EndIf
// -------------------------------------
// Verifica se o cliente possui hardlock ou é especial
// -------------------------------------
If !Empty(aHasp)
	
	
		
	
	/*If FlatMode()
		nWndTop := 0
		nWndLeft := 0
		nAdjust := 30
	Else
		nWndTop := oMainWnd:nTop+125
		nWndLeft := oMainWnd:nLeft+5
		nAdjust := 30
	EndIf*/
	oBaseForm:SetAllClient(.t.)
	oBaseForm:CreateHorizontalBox( "ENCHOICE", 020 )
	oBaseForm:CreateHorizontalBox( "L1", 040 )
	oBaseForm:CreateVerticalBox( "ESQ_SUP", 050,, "L1" )
	oBaseForm:CreateVerticalBox( "DIR_SUP", 050,, "L1" )
	
	oBaseForm:CreateHorizontalBox( "L2", 040 )
	oBaseForm:CreateVerticalBox( "ESQ_INF", 050,, "L2" )
	oBaseForm:CreateVerticalBox( "DIR_INF", 050,, "L2" )

	oBaseForm:oFormBar:AddOk( {|| If(nErrValid > -2,(A020GrvHASP(lEsp,aGetLic,aGetLight,@aHASP,aColigada),oHASP:Refresh()),) } )
	oBaseForm:oFormBar:AddClose( {|| oBaseForm:Deactivate() } )
	oBaseForm:Activate()
	
	
	oBaseForm:SetDlgTitle( "Autorização Hard Lock" )
	oBaseForm:SetDlgSize( { 000, 000, 600, 800 } )
	oBaseForm:SetDlgCentered( .T. )
	
	//DEFINE MSDIALOG oDlg TITLE "Autorização Hard Lock" FROM oSize:aWindSize[1],oSize:aWindSize[2] TO    oSize:aWindSize[3],oSize:aWindSize[4] PIXEL
		
	//DEFINE MSDIALOG oDlg TITLE "Autorização Hard Lock" FROM nWndTop,nWndLeft TO oMainWnd:nBottom-nAdjust,oMainWnd:nRight-10 PIXEL
	//oDlg:lMaximized := .T.
	
	oEnchoice := Msmget():New("ZA1",nReg,4,,,,,             {0,;
             0,;
             100,;
             600};
			,,,,,,oBaseForm:GetPanel( "ENCHOICE" ),,,.F.,,,.T.,aInfo1)
	
	oEnchoice:oBox:Align := CONTROL_ALIGN_ALLCLIENT
	
	/*oPanel1:ReadClientCoors()
	nWidth := oDlg:nWidth/4
	nHeight := oDlg:nHeight/4-30*/
	
	//@oSize:GetDimension("ESQ_SUP","LININI"),oSize:GetDimension("ESQ_SUP","COLINI") MSPANEL oPanel1 PROMPT "" SIZE oSize:GetDimension("ESQ_SUP","XSIZE"),oSize:GetDimension("ESQ_SUP","YSIZE")
	//@03,01 SAY "Total Licenças Genéricas" PIXEL OF OdLG FONT oFontBold
	//@13,01 SCROLLBOX aBox[1] SIZE nHeight-15,nWidth-4 OF oPanel1	
	oPanel1 := oBaseForm:GetPanel( "ESQ_SUP")
	@03,01 SAY oSAy Var "Total Licenças Genéricas" PIXEL OF oPanel1 FONT oFontBold
	oSay:Align := CONTROL_ALIGN_TOP
	@0,0  SCROLLBOX aBox[1] SIZE 10,10 OF oPanel1 
	aBox[1]:Align := CONTROL_ALIGN_ALLCLIENT
	
/*	@03,nWidth SAY "Total Licenças Light" PIXEL OF oPanel1 FONT oFontBold
	@13,nWidth SCROLLBOX aBox[2] SIZE nHeight-15,nWidth-4 OF oPanel1*/
oPanel1 := oBaseForm:GetPanel( "DIR_SUP")
	@03,nWidth SAY oSay Var  "Total Licenças Light" PIXEL OF oPanel1 FONT oFontBold
	oSay:Align := CONTROL_ALIGN_TOP
	@0,0  SCROLLBOX aBox[2] SIZE 10,10 OF oPanel1 
	aBox[2]:Align := CONTROL_ALIGN_ALLCLIENT
	
//	@00,00 MSPANEL oPanel2 PROMPT "" SIZE nHeight,nHeight
	//oPanel2:Align := CONTROL_ALIGN_ALLCLIENT
	
//	@01,01 LISTBOX oHASP FIELDS HEADER "ID","Vencimento","Checkum" OF oPanel2 PIXEL SIZE nWidth-4,nHeight-5 ;
oPanel1 := oBaseForm:GetPanel( "ESQ_INF")
@0,0 LISTBOX oHASP FIELDS HEADER "ID","Vencimento","Checkum" OF oPanel1 PIXEL 100,100;
	ON CHANGE (U_GetRefresh(aObjFld1),U_GetRefresh(aObjFld2))
	oHASP:SetArray(aHASP)
	oHASP:bLine := {|nAt| nAt := oHASP:nAt,{aHasp[nAt][2],aHasp[nAt][3],aHasp[nAt][4]}}
	oHASP:blDblClick := {|| If(lEsp,A020Esp(@aHASP,@oHASP,nLenLic,nLenLight),)}
	oHASP:Align := CONTROL_ALIGN_ALLCLIENT
	oPanel1 := oBaseForm:GetPanel( "DIR_INF")
	@0,0 FOLDER oFolder PROMPT "Licenças Genéricas","Licenças Light","CNPJ","Coligadas" PIXEL OF oPanel1 SIZE 100,210
	oFolder:Align := CONTROL_ALIGN_ALLCLIENT
	//@01,nWidth FOLDER oFolder PROMPT "Licenças Genéricas","Licenças Light","CNPJ","Coligadas" PIXEL SIZE nWidth-4,nHeight-5 OF oPanel2
	
	@-1,-1 SCROLLBOX aBox[3] SIZE 86,148 OF oFolder:aDialogs[1]
	@-1,-1 SCROLLBOX aBox[4] SIZE 86,148 OF oFolder:aDialogs[2]
	
	aBox[3]:Align := CONTROL_ALIGN_ALLCLIENT
	aBox[4]:Align := CONTROL_ALIGN_ALLCLIENT
	
	For nI := 1 To nLenLic
		TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLic[nI]+'"}'),aBox[1],,oFont,,,,.T.,,13,110)
		aObjLic[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLic["+Str(nI)+"], aGetLic["+Str(nI)+"] := u ) }"),aBox[1],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
		If aGetLic[nI] > 0
			aAdd(aObjBtnIfno,TBitmap():New(02+((nI-1)*12),140,12.5 ,12.5,,'rpmperg.png' ,,abox[1],&("{|oObj|CLIA20Tre(oObj,"+sTR(nI)+",aRetTree) }"),/*{||mSGstOP("TESTE")}*/,,.T., ,,,,.t.))
		Endif
	
		aObjLic[ni]:Disable()
	
		TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLic[nI]+'"}'),aBox[3],,oFont,,,,.T.,,13,110)
		aObjFld1[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aHASP[oHASP:nAt][5]["+Str(nI)+"], aHASP[oHASP:nAt][5]["+Str(nI)+"] := u ) }"),aBox[3],10,05,'9999',,,,,,,.T.,,,,,,,.F.)
	Next
	
	For nI := 1 To nLenLight
		TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLight[nI]+'"}'),aBox[2],,oFont,,,,.T.,,13,110)
		aObjLight[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLight["+Str(nI)+"], aGetLight["+Str(nI)+"] := u ) }"),aBox[2],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
		//
		If aGetLight[nI] > 0
	//		TBtnBmp2():New( 000, 000, nWidth, nHeight, cImage, cImage,,, Eval(oBtActionCan:blClicked), ::oPanel, cTooltip, bWhen, lAdjust )
	//        TBtnBmp2():New( 02+((nI-1)*12),140,12.5 ,12.5, 'rpmperg.png' , 'rpmperg.png',,, {|a,b,c,d,e|CLIA20Tre(a,b,c,d,e) }, aBox[2], , , .t. )
			aAdd(aObjBtnIfno,TBitmap():New(02+((nI-1)*12),140,12.5 ,12.5,,'rpmperg.png' ,,abox[2],&("{|oObj|CLIA20Tre(oObj,"+sTR(nI)+") }"),/*{||mSGstOP("TESTE")}*/,,.T., ,,,,.t.))
		Endif
		aObjLight[ni]:Disable()
	
		TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLight[nI]+'"}'),aBox[4],,oFont,,,,.T.,,13,110)
		aObjFld2[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aHASP[oHASP:nAt][6]["+Str(nI)+"], aHASP[oHASP:nAt][6]["+Str(nI)+"] := u ) }"),aBox[4],10,05,'9999',,,,,,,.T.,,,,,,,.F.)
	Next
	
	//ÚÄÄÄÄÄÄÄÄÄ¿
	//³Coligadas³
	//ÀÄÄÄÄÄÄÄÄÄÙ
	aColigada := U_CL_Coligadas(M->ZDP_CLIENT)
	For ni := 1 To Len(aColigada)
		aColigada[ni] := AllTrim(aColigada[ni])
		For nj := 1 To Len(aHASP)
			Aadd(aHASP[nj][8],Val(Subs(aColigada[ni],1,4)))
			Aadd(aHASP[nj][8],Val(Subs(aColigada[ni],5,4)))
		Next nJ
	Next nI
	//ÚÄÄÄÄ¿
	//³CNPJ³
	//ÀÄÄÄÄÙ
	If lEsp .or. ( Upper(SA1->A1_PAIS) <> "BRA" .And. Upper(SA1->A1_PAIS) <> "105")
		aCNPJ := {"11111111111111"}
	EndIf
	
	For ni := 1 To Len(aCNPJ)
		aCNPJ[ni] := AllTrim(aCNPJ[ni])
		For nj := 1 To Len(aHASP)
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],1,2)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],3,3)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],6,3)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],9,4)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],13,2)))
		Next
		aCNPJ[ni] := Transform(aCNPJ[ni],"@R 99.999.999/9999-99")
	Next
	
	@02,02 LISTBOX oCNPJ VAR nCNPJ ITEMS aCNPJ SIZE 86,148 PIXEL OF oFolder:aDialogs[3]
	//oCNPJ:Align := CONTROL_ALIGN_ALLCLIENT
	
	If !Empty(aColigada)
		@02,02 LISTBOX oColig VAR nCNPJ ITEMS aColigada SIZE 86,148 PIXEL OF oFolder:aDialogs[4]
		oColig:Align := CONTROL_ALIGN_ALLCLIENT
	Endif
	
	If !lEsp
		//Aadd(aEnchBar,{"BMPVISUAL" ,{|| U_CLViewCli(M->ZDP_CLIENT)},"Visualizar Cliente","Visualizar"})
		oBaseForm:oFormBAr:AddUserBtn("BMPVISUAL","Visualizar Cliente",{|| U_CLViewCli(M->ZDP_CLIENT)},"Visualizar Cliente", ,.t.)
	EndIf
	Aadd(aEnchBar,{"PRINT02"   ,{|| If(nErrValid > -2,CLPrtHASP(M->ZDP_CLIENT,M->ZDP_SOLICI,M->ZDP_CONTAT,aHASP,lShow,cDriver),)},"Imprimir Autorização - <F9>","Imprimir"})
/*	ACTIVATE MSDIALOG oDlg CENTER ON INIT EnchoiceBar(oDlg,;
	{|| If(nErrValid > -2,(A020GrvHASP(lEsp,aGetLic,aGetLight,@aHASP,aColigada),oHASP:Refresh()),)},;
	{|| oDlg:End()},,aEnchBar)*/
	oBaseForm:DlgActivate()
Else
	ApMsgStop("Cliente não possui hardlock apto a receber licença. Verifique o status de bloqueio dos hardlocks!")
EndIf	
DbSelectArea("ZDP")
DbSetOrder(nZDPOrder)
DbGoTo(nZDPRecno)
Return

Static Function A020Esp(aHASP,oHASP,nLenLic,nLenLight)
Local oDlg
Local nRadio := 1
Local nOpca := 0
Local cID := Space(15)
Local bLine

DEFINE MSDIALOG oDlg TITLE "Autorização Hard Lock" FROM 0,0 TO 142,230 PIXEL

@04,05 TO 55,111 PIXEL

@08,09 RADIO nRadio ITEMS "&Novo","&Editar" SIZE 80,15 PIXEL

DEFINE SBUTTON FROM 57,53 TYPE 1 ENABLE PIXEL ACTION (nOpca := nRadio,oDlg:End())
DEFINE SBUTTON FROM 57,83 TYPE 2 ENABLE PIXEL ACTION oDlg:End()

ACTIVATE MSDIALOG oDlg CENTER

If nOpca == 1
	A020EspDlg(@cID)
	Aadd(aHASP,{.T.,cID,Ctod(""),0,Array(nLenLic),Array(nLenLight),{}})
	AFill(aHASP[Len(aHasp)][5],0)
	AFill(aHASP[Len(aHasp)][6],0)
	bLine := oHASP:bLine
	oHASP:SetArray(aHASP)
	oHASP:bLine := bLine
	oHASP:Refresh()

ElseIf nOpca == 2
	cID := aHASP[oHASP:nAt][2]
	A020EspDlg(@cID)
	aHASP[oHASP:nAt][1] := .T.
	aHASP[oHASP:nAt][2] := cID
	oHASP:DrawSelect()
EndIf
Return

Static Function A020EspDlg(cID)
Local odlg

DEFINE MSDIALOG oDlg TITLE "ID" FROM 0,0 TO 70,180 PIXEL

@05,05 GET cID PIXEL SIZE 80,09 VALID U_A020BlqHl(cId)

DEFINE SBUTTON FROM 20,55 TYPE 1 ENABLE PIXEL ACTION oDlg:End()

ACTIVATE MSDIALOG oDlg CENTER VALID 	!Empty(cID)
Return

User Function A020BlqHl(cId)

Local aArea := GetArea()
Local aAreaZDP := ZDP->(GetArea())
Local lRetorno := .T.

dbSelectArea("ZDP")
dbSetOrder(2)
If MsSeek(xFilial("ZDP")+cId)
	lRetorno := (ZDP->(FieldPos("ZDP_MSBLQD")) <> 0 .And. Empty(ZDP->ZDP_MSBLQD))		
EndIf
If !lRetorno
	ApMsgStop("Hardlock bloqueado!")
EndIf
RestArea(aAreaZDP)
RestArea(aArea)
Return(lRetorno)


Static Function A020GrvHASP(lEsp,aGetLic,aGetLight,aHASP,aColigadas)
Local lShow := GetPvProfString(cUserName,"Show","N",U_CLIniFile()) == "S"
Local cDriver := GetPvProfString(cUserName,"Driver","",U_CLIniFile())
Local nOpca
Local ni,nj
Local cFile := ""
Local aLicenses := {}
Local aSend := {}
Local aNumLic
Local aNumLight
Local cEmail := Space(100)
Local nLen
Local cCodCli := M->ZDP_CLIENT
Default aColigadas := {}
If lEsp
	cCodCli := U_CliCodCli()

	//validade minima para clientes especificos
	If M->ZDP_VALIDA < DateDiffDay(MonthSum(dDataBase, 12), dDataBase) .and. AllTrim(cCodCli) $ "07612|" 
		M->ZDP_VALIDA := DateDiffDay(MonthSum(dDataBase, 12), dDataBase)
	EndIf
EndIf

If !Empty(cCodCli) .and. U_CLValHasp(M->ZDP_CLIENT,M->ZDP_VALIDA,aGetLic,aGetLight,aHASP)
	U_CLCalcHASP(M->ZDP_DTREF,M->ZDP_VALIDA,@aHasp)
	U_CLGrvHASP(aHasp,cCodCli,M->ZDP_DTREF,M->ZDP_VALIDA,M->ZDP_SOLICI,M->ZDP_CONTAT,M->ZDP_OBSERV,lEsp)
	nOpca := U_CliTSend(@cEmail)

	If nOpca > 0
		If nOpca == 1
			CLPrtHASP(cCodCli,M->ZDP_SOLICI,M->ZDP_CONTAT,aHASP,lShow,cDriver)
		Else
			MakeDir("\haspcli\")
			aNumLic := U_CLNumLic()
			aNumLight := U_CLNumLight()

			For ni := 1 To Len(aHASP)
				If aHASP[ni][1] .and. !Empty(aHASP[ni][2])
				    aLicenses := {}
					
					For nj := 1 To Len(aNumLic)
						Aadd(aLicenses,{aNumLic[nj],aHASP[ni][5][nj]})
					Next
	
					For nj := 1 To Len(aNumLight)
						If aNumLight[nj] > 0
							Aadd(aLicenses,{aNumLight[nj],aHASP[ni][6][nj]})
						EndIf
					Next
					
					For nj := 1 To Len(aHASP[ni][7])
						Aadd(aLicenses,{2000+nj,aHASP[ni][7][nj]})
					Next
					//Coloca as Coligadas
					If ( Len(aHASP[ni]) >= 8  )
						For nj := 1 To Len(aHASP[ni][8])
							Aadd(aLicenses,{1000+nj,aHASP[ni][8][nj]})
						Next
					EndIf			
	
					cFile := "\haspcli\" + AllTrim(aHASP[ni][2]) + ".key"
					FErase(cFile)
					U_SaveLicense(SEED, Val(aHASP[ni][2]), aHASP[ni][3], aLicenses, aHASP[ni][4], cFile)
					Aadd(aSend,cFile)
				EndIf
			Next

			nLen := Len(aSend)
			If nLen > 0
				If nLen == 1
					FErase("\haspcli\applylic.key")
					FRename(aSend[1],"\haspcli\applylic.key")
					aSend := {"\haspcli\applylic.key"}
				EndIf

				cDesc := R020Mail(cCodCli,M->ZDP_SOLICI,M->ZDP_CONTAT,aHASP,(nLen == 1),lEsp,aColigadas)
				SetMail(AllTrim(cEmail),"Microsiga - Departamento de Liberação de Senha",cDesc,aSend,.F.,.F.,.T.,.F.,U_CLGARMail(,cCodCli))
				
				For ni := 1 To Len(aSend)
					Ferase(aSend[ni])
				Next
			EndIf
		EndIf
	EndIf
EndIf
Return

User Function MA020Visual(cAlias,nReg,nOpc)
Local oDlg
Local oPanel1
Local oPanel2
Local oFont := TFont():New("MS Sans Serif",0,-7)
Local oFontBold := TFont():New("MS Sans Serif",0,-12,,.T.)
Local oHASP
Local oFolder
Local oFldLic
Local ni,nj
Local nLenLic609
Local nLenLic710
Local nLenLight710
Local nWndTop
Local nWndLeft
Local nAdjust
Local nWidth
Local nHeight

Local aHASP := {}
Local aMemoLic

Local aSayLic609
Local aSayLic710
Local aObjLic609
Local aObjLic710
Local aObjLic811
Local aGetLic609
Local aGetLic710
Local aGetLic811

Local aSayLight710
Local aObjLight710
Local aObjLight811
Local aGetLight710
Local aGetLight811

Local aObjFld1
Local aObjFld2

Local aFldBox := Array(7)

If !U_ValVincHL(SA1->A1_COD) .or. !U_ValSitCli(SA1->A1_COD)
	Return
EndIf

aSayLic609 := U_SayLicen()
nLenLic609 := Len(aSayLic609)
aObjLic609 := Array(nLenLic609)

aSayLic710 := U_Licen710()
nLenLic710 := Len(aSayLic710)
aObjLic710 := Array(nLenLic710)
aObjLic811 := Array(nLenLic710)
aObjFld1 := Array(nLenLic710)

aSayLight710 := U_Light710()
nLenLight710 := Len(aSayLight710)
aObjLight710 := Array(nLenLight710)
aObjLight811 := Array(nLenLight710)
aObjFld2 := Array(nLenLight710)

aGetLic609 := U_The609Licenses(SA1->A1_COD)

U_CLSetVer("7")
U_HLLicenses(SA1->A1_COD,@aGetLic710,@aGetLight710)

U_CLSetVer("8")
U_HLLicenses(SA1->A1_COD,@aGetLic811,@aGetLight811)

DbSelectArea("ZDP")
DbSetOrder(1)
DbSeek(xFilial() + SA1->A1_COD)
ni := 0
While !Eof() .and. SA1->A1_COD == ZDP->ZDP_CLIENT
	ni++
	Aadd(aHASP,{ZDP->ZDP_ID,ZDP->ZDP_VENCTO,ZDP->ZDP_CHKSUM,ZDP_AUTVER,{/*lic genericas*/},{/*lic light*/}})

	aHASP[ni][5] := Array(nLenLic710)
	aHASP[ni][6] := Array(nLenLight710)
	AFill(aHASP[ni][5],0)
	AFill(aHASP[ni][6],0)

	If !Empty(ZDP->ZDP_LICENS)
		aMemoLic := Str2Array(ZDP->ZDP_LICENS)
		For nj := 1 To Len(aMemoLic[1])
			aHASP[ni][5][nj] := aMemoLic[1][nj]
		Next

		For nj := 1 To Len(aMemoLic[2])
			aHASP[ni][6][nj] := aMemoLic[2][nj]
		Next
	EndIf
	DbSkip()
End

If Empty(aHasp)
	Aadd(aHASP,{"","","","",{/*lic genericas*/},{/*lic light*/}})
	aHASP[1][5] := Array(nLenLic710)
	aHASP[1][6] := Array(nLenLight710)
	AFill(aHASP[1][5],0)
	AFill(aHASP[1][6],0)
EndIf

oMainWnd:ReadClientCoors()

If FlatMode()
	nWndTop := 0
	nWndLeft := 0
	nAdjust := 30
Else
	nWndTop := oMainWnd:nTop+125
	nWndLeft := oMainWnd:nLeft+5
	nAdjust := 30
EndIf

DEFINE MSDIALOG oDlg TITLE SA1->A1_COD+" - "+SA1->A1_NOME FROM nWndTop,nWndLeft TO oMainWnd:nBottom-nAdjust,oMainWnd:nRight-10 PIXEL
oDlg:lMaximized := .T.
oDlg:ReadClientCoors()
nWidth := oDlg:nWidth/4
nHeight := oDlg:nHeight/4-10

@00,00 MSPANEL oPanel1 PROMPT "" SIZE nHeight,nHeight
oPanel1:Align := CONTROL_ALIGN_TOP

@03,01 SAY "Total Licenças Genéricas" PIXEL OF oPanel1 FONT oFontBold
@13,01 FOLDER oFldLic PROMPT "6.09","7.10","8.11" PIXEL SIZE nWidth-4,nHeight-15 OF oPanel1

@00,00 SCROLLBOX aFldBox[1] SIZE nHeight-15,nWidth-4 OF oFldLic:aDialogs[1]
aFldBox[1]:Align := CONTROL_ALIGN_ALLCLIENT

@00,00 SCROLLBOX aFldBox[2] SIZE nHeight-15,nWidth-4 OF oFldLic:aDialogs[2]
aFldBox[2]:Align := CONTROL_ALIGN_ALLCLIENT

@00,00 SCROLLBOX aFldBox[3] SIZE nHeight-15,nWidth-4 OF oFldLic:aDialogs[3]
aFldBox[3]:Align := CONTROL_ALIGN_ALLCLIENT


@03,nWidth SAY "Total Licenças Light" PIXEL OF oPanel1 FONT oFontBold
@13,nWidth FOLDER oFldLight PROMPT "7.10","8.11" PIXEL SIZE nWidth-4,nHeight-15 OF oPanel1

@00,00 SCROLLBOX aFldBox[4] SIZE nHeight-15,nWidth-4 OF oFldLight:aDialogs[1]
aFldBox[4]:Align := CONTROL_ALIGN_ALLCLIENT

@00,00 SCROLLBOX aFldBox[5] SIZE nHeight-15,nWidth-4 OF oFldLight:aDialogs[2]
aFldBox[5]:Align := CONTROL_ALIGN_ALLCLIENT

@00,00 MSPANEL oPanel2 PROMPT "" SIZE nHeight,nHeight
oPanel2:Align := CONTROL_ALIGN_ALLCLIENT


@01,01 LISTBOX oHASP FIELDS HEADER "ID","Vencimento","Checkum","Versao" OF oPanel2 PIXEL SIZE nWidth-4,nHeight-5 ;
ON CHANGE (U_GetRefresh(aObjFld1),U_GetRefresh(aObjFld2))
oHASP:SetArray(aHASP)
oHASP:bLine := {|nAt| nAt := oHASP:nAt,{aHasp[nAt][1],aHasp[nAt][2],aHasp[nAt][3],aHasp[nAt][4]}}

@01,nWidth FOLDER oFolder PROMPT "Licenças Genéricas","Licenças Light" PIXEL SIZE nWidth-4,nHeight-5 OF oPanel2

@00,00 SCROLLBOX aFldBox[6] SIZE 86,148 OF oFolder:aDialogs[1]
aFldBox[6]:Align := CONTROL_ALIGN_ALLCLIENT

@00,00 SCROLLBOX aFldBox[7] SIZE 86,148 OF oFolder:aDialogs[2]
aFldBox[7]:Align := CONTROL_ALIGN_ALLCLIENT


For nI := 1 To nLenLic609
	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLic609[nI]+'"}'),aFldBox[1],,oFont,,,,.T.,,13,110)
	aObjLic609[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLic609["+Str(nI)+"], aGetLic609["+Str(nI)+"] := u ) }"),aFldBox[1],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
	aObjLic609[ni]:Disable()
Next


For nI := 1 To nLenLic710
	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLic710[nI]+'"}'),aFldBox[2],,oFont,,,,.T.,,13,110)
	aObjLic710[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLic710["+Str(nI)+"], aGetLic710["+Str(nI)+"] := u ) }"),aFldBox[2],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
	aObjLic710[ni]:Disable()

	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLic710[nI]+'"}'),aFldBox[3],,oFont,,,,.T.,,13,110)
	aObjLic811[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLic811["+Str(nI)+"], aGetLic811["+Str(nI)+"] := u ) }"),aFldBox[3],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
	aObjLic811[ni]:Disable()

	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLic710[nI]+'"}'),aFldBox[6],,oFont,,,,.T.,,13,110)
	aObjFld1[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aHASP[oHASP:nAt][5]["+Str(nI)+"], aHASP[oHASP:nAt][5]["+Str(nI)+"] := u ) }"),aFldBox[6],10,05,'9999',,,,,,,.T.,,,,,,,.F.)
	aObjFld1[ni]:Disable()
Next


For nI := 1 To nLenLight710
	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLight710[nI]+'"}'),aFldBox[4],,oFont,,,,.T.,,13,110)
	aObjLight710[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLight710["+Str(nI)+"], aGetLight710["+Str(nI)+"] := u ) }"),aFldBox[4],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
	aObjLight710[ni]:Disable()

	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLight710[nI]+'"}'),aFldBox[5],,oFont,,,,.T.,,13,110)
	aObjLight811[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aGetLight811["+Str(nI)+"], aGetLight811["+Str(nI)+"] := u ) }"),aFldBox[5],10,05,'9999',,,,,,,.T.,,,,,,,.T.)
	aObjLight811[ni]:Disable()

	TSay():New(02+((nI-1)*12),2,&('{|| "'+aSayLight710[nI]+'"}'),aFldBox[7],,oFont,,,,.T.,,13,110)
	aObjFld2[ni] := TGet():New(02+((nI-1)*12),115,&("{|u| If( PCount() == 0, aHASP[oHASP:nAt][6]["+Str(nI)+"], aHASP[oHASP:nAt][6]["+Str(nI)+"] := u ) }"),aFldBox[7],10,05,'9999',,,,,,,.T.,,,,,,,.F.)
	aObjFld2[ni]:Disable()
Next

ACTIVATE MSDIALOG oDlg CENTER ON INIT EnchoiceBar(oDlg,{|| oDlg:End()},{|| oDlg:End()})

Return

//------------------------------------------

Static Function InfoContrato(cCliente,aCDU,aSMS,lAsk,lEsp)

Local aPanel1  := {}
Local aPanel2  := VPEGetTTVinc()
Local oDlg 
Local oPanel1
Local oPanel2
Local oOk      := LoadBitmap( GetResources(), "LBOK" )
Local oNo      := LoadBitmap( GetResources(), "LBNO" )
Local lRetorno := .T.
Local aColigadas

DEFAULT lAsk := .T.
DEFAULT lEsp := .F.

SA1->(dbSetOrder(1))
SA1->(dbSeek(xFilial("SA1")+cCliente))

/*aColigadas := U_R600CNPJ(, SA1->A1_COD, SA1->A1_LOJA)
If !Empty()
EndIf */

aadd(aPanel1,{VPEIsAsp(),"Contrato DATACENTER TOTVS"})
aadd(aPanel1,{(VPEIsNewCtr().And.!VPEIsOldLogix() .Or. Len(aCDU) > 0),"Contrato nova politica"})
aadd(aPanel1,{VPEIsOldDT(),"Contrato Datasul"})
aadd(aPanel1,{VPEIsDTSendMail(),"Contrato anterior Datasul"})
aadd(aPanel1,{VPEIsOldDT(.T.),"Contrato ECM"})
aadd(aPanel1,{VPEHasRMOldItems(),"Contrato anterior RM"})
aadd(aPanel1,{Len(VPEGetDSVinc())<>0,"Vinculo de contrato Datasul"})
aadd(aPanel1,{VPEIsOldLogix(),"Contrato Constancia"})
aadd(aPanel1,{VPEIsSERIE1(),"Contrato Série 1"})
aadd(aPanel1,{VPEIsRMFirst(),"Contrato RM First"})
                	
DEFINE MSDIALOG oDlg TITLE "Quadro de avisos" FROM 0,0 TO 300,530 PIXEL
@08,005 LISTBOX oPanel1  FIELDS HEADER "","Avisos" FIELDSIZES 14,100 SIZE 113,112 PIXEL OF oDlg
@08,127 LISTBOX oPanel2 FIELDS HEADER "Cliente Vinculados","Estratégico","Tipo Cliente", "MI"  FIELDSIZES 016,020,020,018 SIZE 130,112 PIXEL OF oDlg

oPanel1:SetArray(aPanel1)
oPanel1:bLine := {|| {Iif(aPanel1[oPanel1:nAt,1],oOk,oNo),aPanel1[oPanel1:nAt,2]}}
oPanel2:SetArray(aPanel2)
If !Empty(aPanel2)
	oPanel2:bLine := {|| {aPanel2[oPanel2:nAt][1],aPanel2[oPanel2:nAt][2],aPanel2[oPanel2:nAt][3],If(aPanel2[oPanel2:nAt][4],"Sim","Nao")}}
EndIf
ACTIVATE DIALOG oDlg CENTERED

AI0->(DbSetOrder(1))
AI0->(DbSeek(xFilial("AI0")+cCliente))
If !Empty(AI0->AI0_XVINCH)
	Alert("Cliente vinculado ao código: "+AI0->AI0_XVINCH)
	Return(.F.)
EndIf

If !VPEIsNewCtr()
	/*If ( !lAsk .Or. MsgYesNo("O contrato não esta na nova politica, deseja converter?"))
		lRetorno := .T.
		If aCDU <> Nil
			lRetorno := _VPEConvNew(cCliente,"00",@aCDU,@aSMS)
			Return(lRetorno)
		Else
			lRetorno := .F.
		EndIf
	EndIf*/
	If VPEIsOldDT()
		MsgStop("Realizar a liberação pelo CST-JOI")
		lRetorno := .F.
	/*ElseIf lEsp .And. (!lAsk .Or. MsgYesNo("O contrato não esta na nova politica, deseja gerar TOTVSLIC.KEY?"))
		lRetorno := .T.	*/
	Else
		lRetorno := .F.	
	EndIf
EndIf
If Empty(aSMS) .And. !Empty(aCDU)

	Alert("Este cliente tem somente CDU Ativa.")

EndIf
Return(lRetorno)

//------------------------------------------
User Function SendLicMail(cAlias)

Local oGrid

Pergunte("CLI020",.T.)

oGrid := FWGridProcess():New("CLIA020","Atualização de licenças","Esta rotina envia um e-mail com a atualização de licenças dos clientes que adquiriram novas licenças ou tiveram sua licença expirada",{|lEnd| ProcLicMail(oGrid,@lEnd)},"CLI020")
oGrid:SetMeters(1)
oGrid:Activate()
Return

Static Function ProcLicMail(oGrid,lEnd)

Local aArea     := GetArea()
Local cQuery    := ""
Local cMail     := ""
Local cAliasZDP := GetNextAlias()
Local nQtdNew   := 0
Local nQtdVecto := 0
Local dDataRef  := MV_PAR01
Local cMsg      := ""
Local aCli      := {}

If !Empty(MV_PAR02) .And. MV_PAR03 >= MV_PAR02+31
	Alert("Periodo de Vencto não pode ultrapassar 31 dias")
	Return
EndIf
// ---------------------------------------------------------
// Avalia os contratos e licenças que expiraram
// ---------------------------------------------------------
If !(Empty(MV_PAR02) .Or. Empty(MV_PAR03))
	cQuery := "SELECT 1 FLAG,ZDP_FILIAL,ZDP.R_E_C_N_O_ ZDPRECNO,ZDP_CLIENT,ZDP_DTREF,ZDP_VENCTO "
	cQuery += "FROM "+RetSqlName("ZDP")+" ZDP "
	cQuery += "WHERE "
	cQuery += "ZDP.ZDP_FILIAL = '"+xFilial("ZDP")+"' AND "
	cQuery += "ZDP.ZDP_VENCTO >= '"+DTOS(MV_PAR02)+"' AND "
	cQuery += "ZDP.ZDP_VENCTO <= '"+DTOS(DataValida(MV_PAR03,.T.))+"' AND "
	cQuery += "ZDP.D_E_L_E_T_ = ' ' "	
EndIf
If !Empty(dDataRef)
	If !Empty(cQuery)
		cQuery += "UNION ALL "
	EndIf
	cQuery += "SELECT 2 FLAG,ZDP_FILIAL,ZDP.R_E_C_N_O_ ZDPRECNO,ZDP_CLIENT,ZDP_DTREF,ZDP_VENCTO "
	cQuery += "FROM "+RetSqlName("ZDP")+" ZDP "
	cQuery += "WHERE "
	cQuery += "ZDP.ZDP_FILIAL = '"+xFilial("ZDP")+"' AND "         	
	cQuery += "ZDP.D_E_L_E_T_ = ' ' AND "
	cQuery += "ZDP.ZDP_CLIENT IN ( "
	cQuery += "SELECT SD6.D6_CLIENTE "
	cQuery += "FROM "+RetSqlName("SD6")+" SD6 "
	cQuery += "WHERE "
	cQuery += "SD6.D6_FILIAL = '"+xFilial("SD6")+"' AND "
	//cQuery += "SD6.D6_DATASS >= '"+DTOS(FirstDay(dDataRef))+"' AND "
	//cQuery += "SD6.D6_DATASS <= '"+DTOS(LastDay(dDataRef))+"' AND "	
	cQuery += "SD6.D6_DATASS = '"+DTOS((dDataRef))+"' AND "	
	cQuery += "SD6.D_E_L_E_T_ = ' ' ) "
	cQuery += "ORDER BY 1 "
EndIf
If !Empty(cQuery)
	cQuery := ChangeQuery(cQuery)
	DbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAliasZDP)
	TcSetField(cAliasZDP,"ZDP_DTREF","D",8,0)
	TcSetField(cAliasZDP,"ZDP_VENCTO","D",8,0)
	
	oGrid:SetMaxMeter(ZDP->(LastRec())/12,1,"Gerando licenças...")
	
	While !Eof() .And. (cAliasZDP)->ZDP_FILIAL == xFilial("ZDP")
	
		// ---------------------------------------------------------
		// Pesquisa os contatos do cliente
		// ---------------------------------------------------------
		dbSelectArea("SA1")
		dbSetOrder(1)
		MsSeek(xFilial("SA1")+(cAliasZDP)->ZDP_CLIENT)
		If (Empty(SA1->A1_PAIS) .Or. SA1->A1_PAIS == "105" .Or. SA1->A1_PAIS == "BRA") .And. SA1->A1_EST <> "EX"
			cMail := IIf("@"$SA1->A1__EMCPD,AllTrim(SA1->A1__EMCPD)+";","")
			
			dbSelectArea("SA3")
			dbSetOrder(1)
			If MsSeek(xFilial("SA3")+SA1->A1_VEND)
				cMail += IIf("@"$SA3->A3_EMAIL,AllTrim(SA3->A3_EMAIL)+";","")
		   EndIf
				
			dbSelectArea("AC8")
			dbSetOrder(2)
			MsSeek(xFilial("AC8")+"SA1"+xFilial("SA1")+(cAliasZDP)->ZDP_CLIENT)
			
			While !Eof() .And. xFilial("AC8") == AC8->AC8_FILIAL .And.;
									"SA1" == AC8->AC8_ENTIDA .And.;
									SA1->A1_FILIAL == AC8->AC8_FILENT .And.;
									AllTrim(SA1->A1_COD+SA1->A1_LOJA) == AllTrim(AC8->AC8_CODENT)
									
				DbSelectArea("SU5")
				dbSetOrder(1)
				If MsSeek(xFilial("SU5")+AC8->AC8_CODCON)
										
					If SU5->U5_ATIVO=="1" .And.;
						( SU5->(FieldPos("U5_MSBLQL"))<>0 .Or. !SU5->U5_MSBLQL ) .And.;
						(!Empty(SU5->U5__SENURA) .Or. !Empty(SU5->U5__SENPOR) ) .And.;
						SU5->U5_RECMAIL == "S"
						cMail += IIf("@"$SU5->U5_EMAIL,AllTrim(SU5->U5_EMAIL)+";","")
					EndIf
				EndIf
		
				dbSelectArea("AC8")
				dbSkip()
			EndDo	
			If !Empty(cMail)
				If aScan(aCli,(cAliasZDP)->ZDP_CLIENT)==0
					aadd(aCli,(cAliasZDP)->ZDP_CLIENT)
					ZDP->(MsGoto((cAliasZDP)->ZDPRECNO))
					U_VPELicMail(cMail,(cAliasZDP)->FLAG==2)
					If (cAliasZDP)->FLAG==2
						nQtdNew++
					Else
						nQtdVecto++
					EndIf
				EndIf
			EndIf
		EndIf
		If lEnd
			Exit
		EndIf
		oGrid:SetIncMeter(1)
	
		dbSelectArea(cAliasZDP)
		dbSkip()
	EndDo
	DbSelectArea(cAliasZDP)
	dbCloseArea()		
EndIf
cMsg := StrTran("Clientes novos #1,Clientes com vencimento: #2","#1",AllTrim(Str(nQtdNew)))
cMsg := StrTran(cMsg,"#2",AllTrim(Str(nQtdVecto)))
Aviso("Informações",cMsg,{"Ok"})

RestArea(aArea)
Return

//------------------------------------------

User Function VPELicMail(cMail,lNew,lWS,nMsg,cLicContent,cHardLock,_cPathFile,_aFiles)
         
Local lContinua			:= .T.
Local lFullDeterminado	:= .F.
Local aArea				:= GetArea()
Local aAreaZDP			:= {}
Local aItensCDU			:= {}
Local aItensSMS			:= {}
Local aCNPJs			:= {}
Local aHasp				:= {}
Local aLoadSV			:= {}
Local aLicReadItemsSD6	:= {}
Local aSegSerie			:= {}
Local aFulls			:= VPEGetFulls()
Local aLights			:= VPEGetLights()
Local aOnDemand			:= VPEGetOnDemand()
Local aVPEFulls			:= {}
Local aVPELights		:= {}
Local aReadHL			:= {}
Local cCliente			:= ZDP->ZDP_CLIENT
Local cMessage			:= ""
Local nError			:= 0
Local nX				:= 0
Local nI				:= 0
Local nPos				:= 0
Local aTmp           
Local lCallWSErp
Local aContratos
Local lmeses		  	:= GetMv("TI_L12MESS",,.T.) //Habilita funcionalidade para setar 12 meses para clientes que tem somente CDU ativa
Local _nForFile
Local _cArq				:= SuperGetMv("TI_TTVSCLI",,"\\172.16.93.148\d$\haspcli\") 
DEFAULT lWS 			:= .F.
DEFAULT nMsg 			:= 0
DEFAULT cLicContent 	:= Nil
DEFAULT _cPathFile		:= Nil
DEFAULT _aFiles		:= Nil
lCallWSErp :=  cLicContent != Nil
// ---------------------------------------------------------
// Verifica se o contrato foi migrado para nova politica VPE
// ---------------------------------------------------------
VpeSetMail(cMail)
//VpeSetMail()
M->ZDP_CLIENT  := ZDP->ZDP_CLIENT
U_VPEIsNewModel( ZDP->ZDP_CLIENT,@aItensCDU,@aItensSMS,@aCNPJs,.F. ,,,@aContratos )
AI0->(DbSetOrder(1))
AI0->(DbSeek(xFilial("AI0")+SA1->A1_COD))
// ---------------------------------------------------------
// Para Serie1 não há envio de e-mail.
// ---------------------------------------------------------
If ( !VPEIsSERIE1() .And. !VPEIsRMFirst() )
	//Verifica se cliente esta vinculado a outro cliente
	If !Empty(AI0->AI0_XVINCH )	
		lContinua := .F.
		nMsg      := 4
	EndIf
	
	//Verifica se processo pode continuar
	If lContinua
		aAreaZDP := ZDP->(GetArea())
		aHASP := {}	

		ZDP->(dbSetOrder(1))
		ZDP->(dbSeek(xFilial("ZDP")+ZDP->ZDP_CLIENT))
		While ZDP->(!Eof()) .And. ZDP->ZDP_FILIAL == xFilial("ZDP") .And. ZDP->ZDP_CLIENT == cCliente
			If (ZDP->(FieldPos("ZDP_MSBLQD")) <> 0 .And. Empty(ZDP->ZDP_MSBLQD))	
				If (ZDP->(FieldPos("ZDP_SMSVER")) == 0 .Or. ZDP->ZDP_SMSVER<>"00")
					If ( !Empty(ZDP->ZDP_VPELIC)  )
						aLoadSV := Str2Array(ZDP->ZDP_VPELIC)
					Else
						aLoadSV := {{},{},{}}
					EndIf
					If ZDP->ZDP_VALIDA<=DateDiffDay(MonthSum(dDataBase, 3), dDataBase) .And. lWS
						lcontinua := .f.
						nMsg := 3
					EndIf	
					Aadd( aHASP, { .T.,IIf(Empty(ZDP->ZDP_HASHID),Trim(ZDP->ZDP_ID),ZDP->ZDP_HASHID), ZDP->ZDP_VENCTO, ZDP->ZDP_CHKSUM, aClone(aLoadSV[1]), aClone(aLoadSV[2])})
					If Ascan( aSegSerie, {|x| x[1] == 7003}) == 0 .And. !Empty(ZDP->ZDP_SMSVER)
						Aadd( aSegSerie, { 7003, 09+Val(ZDP->ZDP_SMSVER) })
					EndIf
				EndIf
			EndIf
			ZDP->(dbSkip())
		EndDo
		ZDP->(RestArea(aAreaZDP))
		If !lNew .Or. Len(aHasp) == 1 .Or. lCallWSErp
			// ----------------------------------------------
			// Converte itens de contrato em Slots de liceças   
			// ----------------------------------------------
			aLicReadItemsSD6 := U_VPEAutHardlock(ZDP->ZDP_CLIENT,@aItensCDU,@aItensSMS )
			
		 	//Verifica se possui somente CDU ativa e parâmetro esteja habilitado para a funcionalidade
		 	If lmeses .AND. Len(aItensCDU) > 0 .AND. Empty(aItensSMS)
				nMeses := DateDiffDay(MonthSum(dDataBase, 12), dDataBase)				
			Else
				// --------------------------------
				// Verifico a validade da liberação
				// --------------------------------
				nMeses := U_VPEGetValid(ZDP->ZDP_CLIENT, @nError, .F., @cMessage,ZDP->(FieldPos("ZDP_HASHTP"))<>0 .And. Trim(ZDP->ZDP_HASHTP)$"1|2|3")
			EndIf			
		 	
		 	If nMeses > 0
				// ---------------------------------------------------------
				// Verifica se o cliente é full determinado
				// ---------------------------------------------------------
				lFullDeterminado := U_PF4ChkFull(cCliente,"00")					
				
				// -------------------------------------------------
				// Posiciona o cliente para calcular o segmento do cliente
				// -------------------------------------------------
				dbSelectArea("SA1")			
				dbSetOrder(1)
				MsSeek(xFilial("SA1")+ZDP->ZDP_CLIENT)
				If !Empty(SA1->A1_CODSEG)
					Aadd( aSegSerie, { 7001, Val(SA1->A1_CODSEG)})
				Else
					Aadd( aSegSerie, { 7001, 1})
				EndIf
				
				// ----------------------------------------------------------
				// Carrega as licenças FULLs
				// ----------------------------------------------------------
				aVPEFulls := {}
				For nX:=1 To Len(aFulls)
					nPos := Ascan( aLights, {|x| x[1]==aFulls[nX][1]})
					If ( nPos > 0 .And. !Empty(aLights[nPos][3]) )
						Aadd( aVPEFulls, { aFulls[nX][1], aFulls[nX][2], aClone(aLights[nPos][3])} )
					Else
						Aadd( aVPEFulls, { aFulls[nX][1], aFulls[nX][2]} )
					EndIf
				Next nI
				// ----------------------------------------------------------
				// Carrega as licenças Lights
				// ----------------------------------------------------------
				aVPELights := {}
				For nX:=1 To Len(aLights)
					If ( aLights[nX][1] < 500 .Or. ( aLights[nX][1] >= 4100 .And. !Empty(aLights[nX][3]) ) )
						Aadd( aVPELights, Aclone(aLights[nX]) )
					EndIf
				Next nI
				// ----------------------------------------------------------
				// Acerta a estrutura do HASP
				// ----------------------------------------------------------
				U_VPEHLStruct( aHASP, aVPEFulls, aVPELights, .F., aOnDemand)
				// -----------------------------------------------------------
				// Faz a leitura e distribuição das licenças
				// -----------------------------------------------------------
				U_VPEConvInfoReadSD6( aLicReadItemsSD6, aReadHL, aOnDemand, aVPEFulls, aVPELights , aHasp)
				// -----------------------------------------------------------
				// Faz a limpeza dos arquivos de envio por e-mail
				// -----------------------------------------------------------
				VPEClearSendFiles()

				// --------------------------------------------------
				// Distribuição automatica das licenças no HL ( Se houver apenas 1 ).
				// --------------------------------------------------
				lContinua := .F.
				For nX := 1 To Len(aHasp)
					For nI := 1 to Len(aHasp[nX][ID_FULLS])
						If aHasp[nX][ID_FULLS][nI][2] <> 0
							lContinua := .T.
							Exit
						EndIf
					Next nI
					For nI := 1 To Len(aHasp[nX][ID_LIGHTS])	
						If aHasp[nX][ID_LIGHTS][nI][2] <> 0
							lContinua := .T.
							Exit
						EndIf
					Next nI
					If lContinua
						Exit
					EndIf
				Next nX
				If Len(aHasp) > 1 .And. !lCallWSErp
					lContinua := .F.
					nMsg := 2
				EndIf
				//------------------------------------------------------------------------
				//Verificacao pedida pelo Edu
				//Caso seja WEbService, na liberacao automatica, se a ultima liberação for
				//maior que a				
				//------------------------------------------------------------------------
				If lContinua .And. lWS
					lContinua := ChkDtaLib(M->ZDP_CLIENT,aContratos)
				Endif
				If lContinua				
					// -----------------------------------------------------------
					// Gera os arquivos de envio
					// -----------------------------------------------------------
					VPEBuildTOTVSLic( aHASP, aCNPJs, DataValida(dDatabase+nMeses,.T.), aOnDemand, aSegSerie,/*lDemo*/,/*lDemoNoLocalhost*/,lFullDeterminado)
					// -----------------------------------------------------------
					// Desliga o envio de Datasul e RM
					// -----------------------------------------------------------			
					VPESetDTSendMail(.F.)
					VPESetRMOldItems(.F.)
					//------------------------------------------------------------
					// Rouba o conteudo do totvslic para WS chamado pelos ERPS
					//------------------------------------------------------------
					If cLicContent != Nil .And. !Empty(cHardLock)
						aTmp     := VPEGetSendFiles()
						nI := aScan(aTmp,{|z| cHardLock $z[1] })
						if nI > 0 
							cLicContent := MemoRead(aTmp[nI][1])
						else
							nMsg := 9
						Endif
						lContinua := !Empty(cLicContent)
						For nI := 1 To Len(aTmp)
							Ferase(aTmp[nI][1])
						Next                   

					Else
						//Irá gravar o arquivo em um caminho específico
						If _cPathFile != Nil
							aFiles	:= {}
							aSizes	:= {}
							aTmp	:= VPEGetSendFiles()
							
							ADir(aTmp[1][1], aFiles, aSizes)
							
							//Verifica se arquivo existe
							If File(aTmp[1][1])
								_cPathFile := _cPathFile + aFiles[1]
								//Copia para local especificado
								If !(__CopyFile(aTmp[1][1],_cPathFile))
									lContinua := .F.
								EndIf
								Ferase(aTmp[1][1])
							Else
								lContinua := .F.
							EndIf
						//Preenche array com o caminho e nome do arquivo	
						ElseIf _aFiles != Nil	
								
							aFiles	:= {}
							aSizes	:= {}
							aTmp	:= VPEGetSendFiles()
							
							For _nForFile := 1 To Len(aTmp)
								ADir(aTmp[_nForFile][1], aFiles, aSizes)
								
								//Verifica se arquivo existe
								If File(aTmp[_nForFile][1])
									_cPathFile := _cArq + aFiles[1]
									
									//Copia para local especificado
									If !(__CopyFile(aTmp[1][1],_cPathFile))
										lContinua := .F.
									Else
										//Adiciona ao array os arquivoe e apaga o original gerado
										AADD(_aFiles,{_cPathFile,aFiles[1]})
										Ferase(aTmp[1][1])
									EndIf
								Else
									lContinua := .F.
								EndIf
							
							Next
						Else
							lContinua := VPESendMailAuthorization( ZDP->ZDP_CLIENT, aCNPJs, DataValida(dDataBase+nMeses,.T.), aHasp , IIf(!lWS,IIf(lNew,2,3),4) )
						EndIf
					Endif
					If lContinua
						Begin Transaction
						VPESaveZDP(ZDP->ZDP_CLIENT,dDatabase, nMeses, ZDP->ZDP_SOLICI, ZDP->ZDP_CONTAT, ZDP->ZDP_OBSERV, aHASP, aCNPJs)
						VPEWriteLogHL( ZDP->ZDP_CLIENT, aCNPJs)
						End Transaction
					EndIf
				EndIf
			Else
				nMsg := 3
				lContinua := .F.
			EndIf
		Else
			nMsg := 2
			lContinua := .F.
		EndIf
	EndIf
EndIf
VpeSetMail("")
RestArea(aArea)
Return(lContinua)


//------------------------------------------

User Function CliA020Hash()

Local cHash   := ""
Local cReturn := ""
Local aLoadSV := Str2Array(ZDP->ZDP_VPELIC)
Local aLoadOld:= Str2Array(ZDP->ZDP_LICENS)
Local aLics   := {}
Local nX      := 0

// ----------------------------
// TOTVS - Nova Politica Comercial
// ----------------------------
If !Empty(aLoadSV) .And. Len(aLoadSV) >= 2
	cHash := ""
	// ----------------------------
	// Fulls
	// ----------------------------
	For nX := 1 To Len(aLoadSV[01])
		If aLoadSV[01][nX][02] <> 0
			cHash += AllTrim(Str(aLoadSV[01][nX][01],10))+"|"+AllTrim(Str(IIf(!aLoadSV[01][nX][03],aLoadSV[01][nX][02],0),10))+"|"+AllTrim(Str(IIf(aLoadSV[01][nX][03],aLoadSV[01][nX][02],0),10))+"|"+CRLF
		EndIf	
	Next nX
	// ----------------------------
	// Lights
	// ----------------------------
	For nX := 1 To Len(aLoadSV[02])
		If aLoadSV[02][nX][02] <> 0
			cHash += AllTrim(Str(aLoadSV[02][nX][01],10))+"|"+AllTrim(Str(IIf(!aLoadSV[02][nX][03],aLoadSV[02][nX][02],0),10))+"|"+AllTrim(Str(IIf(aLoadSV[02][nX][03],aLoadSV[02][nX][02],0),10))+"|"+CRLF
		EndIf	
	Next nX	
	cHash := Md5(cHash,2)+"(new)"
	cReturn := cHash
EndIf
// ----------------------------
// Microsiga x Logix
// ----------------------------
If !Empty(aLoadOld) .And. Len(aLoadOld)>=2
	cHash := ""
	aLics := U_CLNumLic()
	For nX := 1 To Len(aLoadOld[01])
		If aLoadOld[01][nX] <> 0
			If nX <= Len(aLics)
				cHash += AllTrim(Str(aLics[nX],10))+"|"+AllTrim(Str(aLoadOld[01][nX],10))+"|"+AllTrim(Str(0,10))+"|"+CRLF
			EndIf
		EndIf
	Next nX
	aLics := U_CLNumLight()
	For nX := 1 To Len(aLoadOld[02])
		If aLoadOld[02][nX] <> 0
			If nX <= Len(aLics)
				cHash += AllTrim(Str(aLics[nX],10))+"|"+AllTrim(Str(aLoadOld[02][nX],10))+"|"+AllTrim(Str(0,10))+"|"+CRLF
			EndIf
		EndIf
	Next nX
	cHash := Md5(cHash,2)+"(old)"
	cReturn += IIf(Empty(cReturn),""," ou ")+cHash
EndIf
Return(cReturn)

Static Function VPESincSlots(aLoadSV)
Local aFulls	    := VPEGetFulls()
Local aLights	    := VPEGetLights()
Local aRet			:= {{},{},}
Local nI
Local nPos

For nI:=1 To Len(aFulls)
	
	Aadd(aRet[1], {aFulls[nI][1],0,.F.})
	
	nPos := Ascan(aLoadSV[1],{|x| x[1]==aFulls[nI][1]})
	If nPos > 0 .And. aLoadSV[1][nPos][2] > 0
		aRet[1][Len(aRet[1])][2] := aLoadSV[1][nPos][2]
		aRet[1][Len(aRet[1])][3] := aLoadSV[1][nPos][3]
	EndIf	
Next nI 

For nI:=1 To Len(aLights)
	
	Aadd(aRet[2], {aLights[nI][1],0,.F.})
	
	nPos := Ascan(aLoadSV[2],{|x| x[1]==aLights[nI][1]})
	If nPos > 0 .And. aLoadSV[2][nPos][2] > 0
		aRet[2][Len(aRet[2])][2] := aLoadSV[2][nPos][2]
		aRet[2][Len(aRet[2])][3] := aLoadSV[2][nPos][3]
	EndIf	
Next nI

If Len(aLoadSV) > 2 .And. ValType(aLoadSV[3])=="A"
	aRet[3] := aClone(aLoadSV[3])
EndIf

aLoadSV := aClone(aRet)

Return

// -----------------------------------------

User Function A020DlgConv()
Local oDlg
Local oLayer
Local oGetSrc
Local oGetTrg
Local oBtnSrc
Local oBtnTrg
Local oOk
Local oCancel
Local cGetSrc
Local cGetTrg

DEFINE MSDIALOG oDlg FROM 000,000 TO 190,400 PIXEL

// ----------------------------------
// Construção do Layer
// ----------------------------------
oLayer := FwLayer():New()
oLayer:Init( oDlg )

oLayer:addLine( "LINE0", 100, .F.)
oLayer:addCollumn( "COL1",100, .T. , "LINE0")
oLayer:addWindow( "COL1", "WIN1", "Converter SIGAMAT.EMP de RDD CTREE para DBF", 100, .F., .T.,, "LINE0")

// -----------------------------------------------
// Recupero panel para criar os demais componentes
// ------------------------------------------------
oBox := oLayer:getWinPanel( "COL1", "WIN1", "LINE0" )

@ 001,005 SAY "SIGAMAT.EMP Origem:" OF oBox PIXEL
@ 010,005 GET oGetSrc VAR cGetSrc SIZE 170,011 OF oBox Pixel
@ 020,350 BTNBMP oBtnSrc RESOURCE "Folder5" SIZE 024,024 OF oBox;
	ACTION ( cGetSrc := cGetFile('File|SIGAMAT.EMP','SIGAMAT.EMP origem',,,.T.,GETF_LOCALHARD,.F.), oGetSrc:Refresh() )
	

@ 030,005 SAY "SIGAMAT.DBF Destino:" OF oBox PIXEL
@ 040,005 GET oGetTrg VAR cGetTrg SIZE 170,011 OF oBox Pixel
@ 080,350 BTNBMP oBtnTrg RESOURCE "Folder5" SIZE 024,024 OF oBox;
	ACTION ( cGetTrg := cGetFile('File|SIGAMAT.DBF','Pasta destino',,,.F.,GETF_LOCALHARD+GETF_RETDIRECTORY,.F.), oGetTrg:Refresh() )

DEFINE SBUTTON oOk FROM 060,120 TYPE 1 OF oBox ENABLE;
	ACTION MsgRun("Convertendo...",,{|| A020Convert(cGetSrc,cGetTrg)});
	ONSTOP "Converter"
	
DEFINE SBUTTON oCancel FROM 060,150 TYPE 2 OF oBox ENABLE;
	ACTION oDlg:End()
	
ACTIVATE MSDIALOG oDlg CENTERED

Return

// -----------------------------------------

Static Function A020Convert(cSource,cTarget)
Local cTrgFile := CriaTrab(,.F.)
Local cAlias   := Alias()
Local lError   := .F.
Local bError   := ErrorBlock({|e| A020ErrConv(e,@lError)})
If Empty(cSource)
	MsgStop("Informe o SIGAMAT.EMP origem!")
	Return
EndIf

If Empty(cTarget)
	MsgStop("Informe a pasta destino!")
	Return
EndIf

__CopyFile(cSource,"\AP5CLI2\"+cTrgFile+".EMP")

If !File("\AP5CLI2\"+cTrgFile+".EMP")
	MsgStop("Não foi possível o arquivo "+cSource)
	Return
EndIf

Begin Sequence
dbUseArea( .T., "CTREECDX", "\AP5CLI2\"+cTrgFile+".EMP", "EMPTRG" )
If lError
	MsgStop("O arquivo "+cSource+" não está no formato CTREE!")
	EMPTRG->(dbCloseArea())
	If !Empty(cAlias)
		dbSelectArea(cAlias)
	EndIf
	ErrorBlock(bError)
	Return
EndIf
End Sequence
ErrorBlock(bError)

COPY TO "\AP5CLI2\"+cTrgFile+".DBF" VIA "DBFCDXADS"

If !File("\AP5CLI2\"+cTrgFile+".DBF")
	MsgStop("Não foi possível converter o arquivo"+cSource)
	Return
EndIf

EMPTRG->(dbCloseArea())
If !Empty(cAlias)
	dbSelectArea(cAlias)
EndIf

__CopyFile("\AP5CLI2\"+cTrgFile+".DBF",cTarget)
      
FErase("\AP5CLI2\"+cTrgFile+".EMP")
FErase("\AP5CLI2\"+cTrgFile+".DBF")

Return


Static Function A020ErrConv(e,lError)
lError := .T.
Return

//---------------------------

User Function A020Log(nOpc)

Local oBrowse	:= Nil
Local oButton	:= Nil
Local oDlg		:= Nil
Local oDlgGeral	:= Nil
Local aCoors  	:= FWGetDialogSize(oMainWnd)
Local aHeader	:= {}
Local dDataI    := MonthSub(Date(),1)
Local dDataF    := Date()
Local aItens	:= {'1-Sim','2-Não','3-Ambos'}
Local cLibEsp	:= aItens[1]
Local oCombo	:= Nil
Local lOk		:= .F.
Local cFilter	:= ""

Private aRotina := {}

Default nOpc 	:= 1

If nOpc == 2 //Histórico Geral
	DEFINE MSDIALOG oDlgGeral FROM  96,4 TO 280,365 TITLE OemToAnsi("Parâmetros") PIXEL
	@ 10,10 Say OemToAnsi("Data Inicial") SIZE 275, 10 OF oDlgGeral PIXEL
	@ 10,70 MsGet oData  Var dDataI PICTURE "@D" SIZE 50, 10 Of oDlgGeral PIXEL

	@ 25,10 Say OemToAnsi("Data Final") SIZE 275, 10 OF oDlgGeral PIXEL
	@ 25,70 MsGet oData  Var dDataF PICTURE "@D" SIZE 50, 10 Of oDlgGeral PIXEL

	@ 40,10 Say OemToAnsi("Liberação Especial?") SIZE 275, 10 OF oDlgGeral PIXEL
	oCombo := TComboBox():New(40,70,{|u|if(PCount()>0,cLibEsp:=u,cLibEsp)},aItens,100,20,oDlgGeral,,,,,,.T.,,,,,,,,,'cLibEsp')

	DEFINE SBUTTON FROM 70,143 TYPE 1 ACTION {|| lOk := .T., oDlgGeral:End() } ENABLE OF oDlgGeral

	ACTIVATE MSDIALOG oDlgGeral Centered

	If Left(cLibEsp,1) == '1' //Sim
		cFilter := " .AND. ZA3_LIBESP = 'T' "
	ElseIf Left(cLibEsp,1) == '2' //Não
		cFilter := " .AND. ZA3_LIBESP = 'F' "
	EndIf

Else //Histórico Cliente
	dbSelectArea("SA1")
	dbSetOrder(1)
	MsSeek(xFilial()+PadR(ZA3->ZA3_CLIENT,Len(SA1->A1_COD)))
	lOk := .T.

EndIf

If lOk
	//-------------------------------------------------------------------
	// Monta o Header
	//-------------------------------------------------------------------
	aadd(aHeader,{"Cliente"			,"Cliente"			,"C",6,0,""})
	aadd(aHeader,{"Dt.Liberação"	,"Dt.Liberação"		,"D",8,0,""}) 
	aadd(aHeader,{"Usuário"			,"Usuário"  		,"C",15,0,""})
	aadd(aHeader,{"SenhaP"			,"SenhaP"    		,"C",Len(ZA3->ZA3_SENHAP),0,""})
	aadd(aHeader,{"HardLock"		,"HardLock"   	 	,"C",Len(ZA3->ZA3_MODULO),0,""})
	aadd(aHeader,{"Versão"			,"Versão"    		,"C",Len(ZA3->ZA3_VERSAO),0,""})
	aadd(aHeader,{"Vencimento"		,"Vencimento"   	,"D",8,0,""}) 
	aadd(aHeader,{"Lib. Especial"	,"Lib. Especial"	,"C",1,0,""}) 

	If nOpc == 1
		DEFINE MSDIALOG oDlg TITLE 'Histórico de Liberação'+SA1->A1_NOME FROM aCoors[1],aCoors[2] TO aCoors[3],aCoors[4] STYLE nOR(WS_VISIBLE,WS_POPUP) PIXEL
		//-------------------------------------------------------------------
		// Define o Browse
		//-------------------------------------------------------------------
		DEFINE FWFORMBROWSE oBrowse DATA TABLE ALIAS "ZA3" FILTERFIELDS aHeader NO SEEK NO DETAILS PROFILEID "0" FILTERDEFAULT ("ZA3_FILIAL='"+xFilial("ZA3")+"' .AND. ZA3_CLIENT=='"+ZDP->ZDP_CLIENT+"'.AND. ZA3_TIPOEM=='H' .AND. ZA3_DATA>='20091001'") OF oDlg 

	Else
		DEFINE MSDIALOG oDlg TITLE 'Histórico de Liberação' FROM aCoors[1],aCoors[2] TO aCoors[3],aCoors[4] STYLE nOR(WS_VISIBLE,WS_POPUP) PIXEL
		//-------------------------------------------------------------------
		// Define o Browse
		//-------------------------------------------------------------------
		DEFINE FWFORMBROWSE oBrowse DATA TABLE ALIAS "ZA3" FILTERFIELDS aHeader NO SEEK NO DETAILS PROFILEID "0" FILTERDEFAULT ("ZA3_FILIAL='"+xFilial("ZA3")+"' .AND. ZA3_DATA >= '"+DToS(dDataI)+"' .AND. ZA3_DATA <= '"+DToS(dDataF)+"'"+cFilter) OF oDlg 

	EndIf

	oBrowse:SetDescription('Histórico de Liberação')
	//-------------------------------------------------------------------
	// Adiciona as colunas do Browse
	//-------------------------------------------------------------------
	ADD COLUMN oColumn DATA { || ZA3->ZA3_CLIENT	} TITLE aHeader[1][1] SIZE aHeader[1][4] TYPE aHeader[1][3] OF oBrowse
	ADD COLUMN oColumn DATA { || ZA3->ZA3_DATA 		} TITLE aHeader[2][1] SIZE aHeader[2][4] TYPE aHeader[2][3] OF oBrowse
	ADD COLUMN oColumn DATA { || ZA3->ZA3_USUARI	} TITLE aHeader[3][1] SIZE aHeader[3][4] TYPE aHeader[3][3] OF oBrowse
	ADD COLUMN oColumn DATA { || ZA3->ZA3_SENHAP	} TITLE aHeader[4][1] SIZE aHeader[4][4] TYPE aHeader[4][3] OF oBrowse
	ADD COLUMN oColumn DATA { || ZA3->ZA3_MODULO	} TITLE aHeader[5][1] SIZE aHeader[5][4] TYPE aHeader[5][3] OF oBrowse
	ADD COLUMN oColumn DATA { || ZA3->ZA3_VERSAO	} TITLE aHeader[6][1] SIZE aHeader[6][4] TYPE aHeader[6][3] OF oBrowse
	ADD COLUMN oColumn DATA { || ZA3->ZA3_VENC		} TITLE aHeader[7][1] SIZE aHeader[7][4] TYPE aHeader[7][3] OF oBrowse
	ADD COLUMN oColumn DATA { || IIf(ZA3->ZA3_LIBESP,"Sim","Não")	} TITLE aHeader[8][1] SIZE aHeader[8][4] TYPE aHeader[8][3] OF oBrowse
	//-------------------------------------------------------------------
	// Ativação do Browse
	//-------------------------------------------------------------------
	ADD BUTTON oButton TITLE "Detalhe" ACTION {||A020DetLog()} OPERATION MODEL_OPERATION_VIEW   OF oBrowse //"Visualizar"
	ACTIVATE FWFORMBROWSE oBrowse

	ACTIVATE DIALOG oDlg CENTERED
EndIf

Return

Static Function A020DetLog()

Local aLic := Str2Array(ZA3->ZA3_HASP)
Local aLights := {}
Local aHeader := {}
Local aCoors  := FWGetDialogSize(oMainWnd)
Local oDlg    := Nil
Local oColumn := Nil
Local oButton := Nil
Local oTable := Nil 
Local aLight := FWVPEGetLights()
Local aFull	:= FWVPEGetFulls()
Local cTexto := ""
Local nX		:= 0
 

If !Empty(aLic) .And. Len(aLic)>=4 .And. Len(aLic[1]) >= 1 .And. Len(aLic[1][1])>=3
	aLights := {}
	For nX := 1 To Len(aLic[1])
		aadd(aLights,{aLic[1][nX][1],aLic[1][nX][2],aLic[1][nX][3]})
	Next nX
	For nX := 1 To Len(aLic[2])
		If Len(aLic[2][nX])>=3
			aadd(aLights,{aLic[2][nX][1],aLic[2][nX][2],aLic[2][nX][3]})
		Else
			aadd(aLights,{aLic[2][nX][1],aLic[2][nX][2],.F.})
		EndIf
	Next nX
	//-------------------------------------------------------------------
	// Monta o Header
	//-------------------------------------------------------------------
	aadd(aHeader,{"Cód.Licença","Cód.Licença" ,"C",4,0,""}) 
	aadd(aHeader,{"Quantidade","Quantidade"   ,"N",12,0,""})
	aadd(aHeader,{"OnDemand","OnDemand"      	,"L",10,0,""})
	aadd(aHeader,{"Descrição","Descrição"		,"C",40,0,""})
	//-------------------------------------------------------------------
	// Define a janela do Browse
	//-------------------------------------------------------------------
	DEFINE MSDIALOG oDlg TITLE "Detalhe das licença liberadas" FROM aCoors[1],aCoors[2] TO aCoors[3],aCoors[4] STYLE nOR(WS_VISIBLE,WS_POPUP) PIXEL
	//-------------------------------------------------------------------
	// Define o Browse
	//-------------------------------------------------------------------
	DEFINE FWFORMBROWSE oTable DATA ARRAY ARRAY aLights FILTERFIELDS aHeader OF oDlg 
	oTable:SetDescription("Detalhe das licença liberadas")	
	//-------------------------------------------------------------------
	// Adiciona as colunas do Browse
	//-------------------------------------------------------------------
	ADD COLUMN oColumn DATA { || aLights[oTable:At(),1] } TITLE aHeader[1][1] SIZE aHeader[1][4] TYPE aHeader[1][3] OF oTable
	ADD COLUMN oColumn DATA { || aLights[oTable:At(),2] } TITLE aHeader[2][1] SIZE aHeader[2][4] TYPE aHeader[2][3] OF oTable
	ADD COLUMN oColumn DATA { || IIf(aLights[oTable:At(),3],"Ligado","Desligado") } TITLE aHeader[3][1] SIZE aHeader[3][4] TYPE aHeader[3][3] OF oTable
	ADD COLUMN oColumn DATA { || cTexto:="",nLic := aScan(aFull,{|x| x[1] == aLights[oTable:At()][1]}),IIf(nLIc>0,cTexto := aFull[nLic][2],cTexto := aLights[aScan(aLights,{|x| x[1] == aLights[oTable:At()][1]})][2]) } TITLE aHeader[4][1] SIZE aHeader[4][4] TYPE aHeader[4][3] OF oTable
	//-------------------------------------------------------------------
	// Ativação do Browse
	//-------------------------------------------------------------------
	ADD BUTTON oButton TITLE "" ACTION "" OPERATION MODEL_OPERATION_VIEW   OF oTable
	ACTIVATE FWFORMBROWSE oTable
	//-------------------------------------------------------------------
	// Ativação do janela
	//-------------------------------------------------------------------
	ACTIVATE MSDIALOG oDlg
Else
	Alert("Log não disponível pois foi gerado na versão 2002 do LS")
EndIf
Return

Static Function a020Excel()

Local nHandle := 0
Local cLinha  := 0 
Local aLic	 := {}
Local aLights  := {}
Local nX     := 0
Local aLight := FWVPEGetLights()
Local aFull	:= FWVPEGetFulls()
Local nLic	:= 0
Local cTexto := ""
dbSelectArea("ZA3")
Set Filter To ZA3_FILIAL='  ' .AND. ZA3_TIPOEM=='H' .AND. DTOS(ZA3_DATA)>='20110101'
dbGotop()

nHandle := Fcreate("excel_lc.txt")

While !Eof()

	aLic := Str2Array(ZA3->ZA3_HASP)
	If !Empty(aLic) .And. Len(aLic)>=4 .And. Len(aLic[1]) >= 1 .And. Len(aLic[1][1])>=3
		aLights := {}
		For nX := 1 To Len(aLic[1])
			aadd(aLights,{aLic[1][nX][1],aLic[1][nX][2],aLic[1][nX][3]})
		Next nX
		For nX := 1 To Len(aLic[2])
			If Len(aLic[2][nX])>=3
				aadd(aLights,{aLic[2][nX][1],aLic[2][nX][2],aLic[2][nX][3]})
			Else
				aadd(aLights,{aLic[2][nX][1],aLic[2][nX][2],.F.})
			EndIf
		Next nX
		For nX := 1 To Len(aLights)
		
			nLic := aScan(aFull,{|x| x[1] == aLights[nX][1]})
			If nLic == 0
				nLic := aScan(aLight,{|x| x[1] == aLights[nX][1]})
				If nLic ==0
					cTexto := "N/D"
				Else
					cTexto := aLight[nLic][2]
				EndIf
			Else
				cTexto := aFull[nLic][2] 	
			EndIf
					
		
			cLinha := cValToChar(ZA3->ZA3_DATA)+";"+;
						cValToChar(ZA3->ZA3_CLIENT)+";"+;
						cValToChar(ZA3->ZA3_USUARI)+";"+;
						cValToChar(ZA3->ZA3_SENHAP)+";"+;
						cValToChar(ZA3->ZA3_MODULO)+";"+;
						cValToChar(ZA3->ZA3_VERSAO)+";"+;
						cValToChar(ZA3->ZA3_VENC)+";"+;
						cValToChar(ZA3->ZA3_LIBESP)+";"+;
						cValToChar(aLights[nX][1])+";"+;
						cTexto+";"+;
						cValToChar(aLights[nX][2])+";"
				
			If Len(aLights[nX])>2
				cLinha += cValToChar(aLights[nX][3])+CRLF
			Else
				cLinha += cValToChar(.F.)+CRLF
			EndIf
						
			FWrite(nHandle,cLinha,Len(cLinha))
			
		Next nX
		
	EndIf
	dbSelectArea("ZA3")
	dbSkip()
EndDo


fClose(nHandle)

__copyfile("Excel_lc.txt","c:\Excel_lc.txt")
FErase("Excel_lc.txt")

Alert("TERMINOU")


Static Function a020Excel2()

Local nHandle := 0
Local cLinha  := 0 
Local nX     := 0
Local cTexto := ""
dbSelectArea("ZDP")
Set Filter To DTOS(ZDP_MSBLQD)=='        ' 
dbGotop()

nHandle := Fcreate("excel_lc2.txt")

While !Eof()
	DbSelectArea("SA1")
	dbSetOrder(1)
	DbSeek(xFilial("SA1")+ZDP->ZDP_CLIENT)
	
	cLinha := cValToChar(ZDP->ZDP_CLIENT)+";"+;
					cValToChar(ZDP->ZDP_ID)+";"+;
					cValToChar(ZDP->ZDP_HASHID)+";"+;
					cValToChar(ZDP->ZDP_HASHTP)+";"+;
					cValToChar(ZDP->ZDP_VENCTO)+";"+;
					cValToChar(SA1->A1_NOME)+";"
	cTexto := cLinha

	DbSelectArea("SA1")
	DbOrderNickName("A1_VINCHL")
	If DbSeek(xFilial("SA1")+ZDP->ZDP_CLIENT)
	
		cLinha += SA1->A1_COD+";"+SA1->A1_NOME+CRLF
	
		While !Eof() .And. AllTrim(ZDP->ZDP_CLIENT) == AllTrim(SA1->A1_VINCHL)
			
			dbSelectArea("SA1")
			dbSkip()
			
			If !Eof() .And. AllTrim(ZDP->ZDP_CLIENT) == AllTrim(SA1->A1_VINCHL)
			
				cLinha += cTexto + SA1->A1_COD+";"+SA1->A1_NOME+CRLF
				
			EndIf	
		EndDo
	Else
		cLinha += "N/D"+";"+"Não possui cliente vinculado"+CRLF
	EndIf
	FWrite(nHandle,cLinha,Len(cLinha))
			
	dbSelectArea("ZDP")
	dbSkip()
EndDo


fClose(nHandle)

__copyfile("Excel_lc2.txt","c:\Excel_lc2.txt")
FErase("Excel_lc2.txt")

Alert("TERMINOU")

/**
* Retorna .T. se a data de ultima revisao do contrato é menor que a ultima data de liberacao
*/
Static function ChkDtaLib(cCliente,aContratos)
Local lContinua := .F.
Local dLastLib := LastLib(cCliente)
Local aOrd
Local dMaiorAlteracao 
if Len(aContratos) > 0
	aOrd := aSort(aContratos,,,{|x,y|x[4]<Y[4]})
	dMaiorAlteracao := aOrd[1][4]
	lContinua := !(dMaiorAlteracao > dLastLib)
Endif
Return lContinua

Static Function LastLib(cCliente)
Local cRet := MPSysExecScalar("SELECT MAX(ZA3_DATA) ZA3_DATA FROM " + RetSqlName("ZA3") + " WHERE ZA3_CLIENT = '"+cCliente+"' AND D_E_L_E_T_ = ' ' ","ZA3_DATA")
Local dDate := CTOD("01/01/1990")
If (!Empty(cRet ))
	dDate := SToD(cRet)
Endif
Return dDate
